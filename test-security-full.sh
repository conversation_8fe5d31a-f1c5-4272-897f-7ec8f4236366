#!/bin/bash

# CareMate Full Security Testing Script
# Starts all services and runs comprehensive security tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 CareMate Security Testing Suite${NC}"
echo "===================================="

# Function to check if a service is running
check_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}Waiting for $name to start...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $name is running${NC}"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $name failed to start after $max_attempts attempts${NC}"
    return 1
}

# Function to cleanup processes
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    
    # Stop Tyk Gateway
    if [ -f tyk/docker-compose.yml ]; then
        cd tyk
        docker-compose down > /dev/null 2>&1 || true
        cd ..
    fi
    
    # Stop API server
    if [ ! -z "$API_PID" ]; then
        kill $API_PID > /dev/null 2>&1 || true
    fi
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

# Set trap for cleanup on exit
trap cleanup EXIT

echo -e "\n${BLUE}📋 Pre-flight Checks${NC}"
echo "===================="

# Check if required files exist
if [ ! -f "api/package.json" ]; then
    echo -e "${RED}❌ API project not found${NC}"
    exit 1
fi

if [ ! -f "tyk/docker-compose.yml" ]; then
    echo -e "${RED}❌ Tyk configuration not found${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Project structure verified${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker is running${NC}"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js is available${NC}"

echo -e "\n${BLUE}🚀 Starting Services${NC}"
echo "==================="

# Start Tyk Gateway
echo -e "${YELLOW}Starting Tyk Gateway...${NC}"
cd tyk
docker-compose up -d
cd ..

# Wait for Redis to be ready
check_service "http://localhost:6379" "Redis" || exit 1

# Wait for Tyk Gateway to be ready
check_service "http://localhost:8181/hello" "Tyk Gateway" || exit 1

# Start API server
echo -e "${YELLOW}Starting API server...${NC}"
cd api

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing API dependencies...${NC}"
    npm install > /dev/null 2>&1
fi

# Start API server in background
npm start > api.log 2>&1 &
API_PID=$!
cd ..

# Wait for API server to be ready
check_service "http://localhost:3001/api/health" "API Server" || exit 1

echo -e "\n${GREEN}🎉 All services are running!${NC}"

echo -e "\n${BLUE}🔍 Running Security Tests${NC}"
echo "========================="

# Install test dependencies if needed
cd tyk
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing test dependencies...${NC}"
    npm init -y > /dev/null 2>&1
    npm install axios > /dev/null 2>&1
fi

# Run comprehensive security tests
echo -e "${YELLOW}Executing security test suite...${NC}"
node test-security-comprehensive.js

# Capture test exit code
TEST_EXIT_CODE=$?

echo -e "\n${BLUE}📊 Test Results${NC}"
echo "==============="

if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}🎉 All security tests passed!${NC}"
    echo -e "${GREEN}Your CareMate security implementation is robust.${NC}"
else
    echo -e "${RED}⚠️  Some security tests failed.${NC}"
    echo -e "${YELLOW}Please review the test output above and fix any issues.${NC}"
fi

echo -e "\n${BLUE}📝 Additional Recommendations${NC}"
echo "=============================="
echo "1. Review API server logs: api/api.log"
echo "2. Review Tyk Gateway logs: docker-compose logs gateway"
echo "3. Monitor security events in production"
echo "4. Regularly update security configurations"
echo "5. Perform penetration testing with external tools"

echo -e "\n${BLUE}🔗 Useful URLs${NC}"
echo "=============="
echo "• API Health: http://localhost:3001/api/health"
echo "• API Docs: http://localhost:3001/docs/"
echo "• Tyk Gateway: http://localhost:8181/hello"
echo "• Public API: http://localhost:8181/caremate/api/"
echo "• Protected API: http://localhost:8181/caremate/protected/"

# Keep services running for manual testing if tests passed
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "\n${GREEN}Services will remain running for manual testing.${NC}"
    echo -e "${YELLOW}Press Ctrl+C to stop all services.${NC}"
    
    # Wait for user interrupt
    while true; do
        sleep 1
    done
else
    exit $TEST_EXIT_CODE
fi
