// CareMate Request/Response Transformation Middleware for Tyk Gateway
// Implements data sanitization, header filtering, and response transformation

var CareMateTransform = new TykJS.TykMiddleware.NewMiddleware({});

// Configuration for transformations
var transformConfig = {
    // Headers to remove from requests (security)
    removeRequestHeaders: [
        "x-forwarded-server",
        "x-forwarded-host", 
        "x-real-ip",
        "x-original-forwarded-for",
        "proxy-authorization",
        "proxy-authenticate"
    ],
    
    // Headers to remove from responses (security)
    removeResponseHeaders: [
        "server",
        "x-powered-by",
        "x-aspnet-version",
        "x-aspnetmvc-version",
        "x-sourcefiles",
        "x-debug-token",
        "x-debug-token-link"
    ],
    
    // Headers to add to responses (security)
    addResponseHeaders: {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY", 
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
        "Cache-Control": "no-store, no-cache, must-revalidate, private",
        "Pragma": "no-cache",
        "Expires": "0"
    },
    
    // Sensitive data patterns to mask in logs
    sensitivePatterns: [
        {
            pattern: /"password"\s*:\s*"[^"]*"/gi,
            replacement: '"password":"[REDACTED]"'
        },
        {
            pattern: /"token"\s*:\s*"[^"]*"/gi,
            replacement: '"token":"[REDACTED]"'
        },
        {
            pattern: /"secret"\s*:\s*"[^"]*"/gi,
            replacement: '"secret":"[REDACTED]"'
        },
        {
            pattern: /"apiKey"\s*:\s*"[^"]*"/gi,
            replacement: '"apiKey":"[REDACTED]"'
        },
        {
            pattern: /"authorization"\s*:\s*"[^"]*"/gi,
            replacement: '"authorization":"[REDACTED]"'
        }
    ],
    
    // Request body size limits by endpoint
    bodySizeLimits: {
        "POST:/auth/login": 1024,        // 1KB
        "POST:/auth/register": 2048,     // 2KB
        "POST:/user": 4096,              // 4KB
        "PUT:/user": 4096,               // 4KB
        "POST:/facility": 8192,          // 8KB
        "PUT:/facility": 8192,           // 8KB
        "default": 1048576               // 1MB default
    },
    
    // Content-Type validation
    allowedContentTypes: [
        "application/json",
        "application/x-www-form-urlencoded",
        "multipart/form-data",
        "text/plain"
    ]
};

// Helper function to sanitize sensitive data
function sanitizeData(data) {
    if (!data) return data;
    
    var sanitized = data;
    for (var i = 0; i < transformConfig.sensitivePatterns.length; i++) {
        var pattern = transformConfig.sensitivePatterns[i];
        sanitized = sanitized.replace(pattern.pattern, pattern.replacement);
    }
    return sanitized;
}

// Helper function to validate content type
function isValidContentType(contentType) {
    if (!contentType) return true; // Allow requests without content-type for GET requests

    var mainType = (contentType || "").split(';')[0].trim().toLowerCase();
    return transformConfig.allowedContentTypes.indexOf(mainType) !== -1;
}

// Helper function to get body size limit for endpoint
function getBodySizeLimit(method, path) {
    var endpointKey = method + ":" + path;
    return transformConfig.bodySizeLimits[endpointKey] || transformConfig.bodySizeLimits.default;
}

// Request transformation middleware
CareMateTransform.NewProcessRequest(function(request, session, config) {
    log("CareMate Transform: Processing request transformation for " + request.RequestURI);
    
    // Get path and method
    var fullPath = (request.RequestURI || "").split('?')[0];
    var method = (request.Method || "GET").toUpperCase();
    var path = fullPath;
    
    // Strip listen path prefix
    if (fullPath.indexOf('/caremate/protected/') === 0) {
        path = fullPath.substring('/caremate/protected'.length);
    } else if (fullPath.indexOf('/caremate/api/') === 0) {
        path = fullPath.substring('/caremate/api'.length);
    }
    
    // Validate Content-Type
    var contentType = request.Headers["content-type"] || request.Headers["Content-Type"];
    if (!isValidContentType(contentType)) {
        log("CareMate Transform: Invalid content type: " + contentType);
        request.ReturnOverrides.ResponseCode = 415;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Unsupported Media Type",
            error: "INVALID_CONTENT_TYPE",
            allowedTypes: transformConfig.allowedContentTypes
        });
        return CareMateTransform.ReturnData(request, {});
    }
    
    // Check request body size
    var bodySize = request.ContentLength || 0;
    var maxSize = getBodySizeLimit(method, path);
    if (bodySize > maxSize) {
        log("CareMate Transform: Request body too large: " + bodySize + " > " + maxSize);
        request.ReturnOverrides.ResponseCode = 413;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Request body too large",
            error: "REQUEST_TOO_LARGE",
            maxSize: maxSize,
            actualSize: bodySize
        });
        return CareMateTransform.ReturnData(request, {});
    }
    
    // Remove sensitive headers from request
    for (var i = 0; i < transformConfig.removeRequestHeaders.length; i++) {
        var header = transformConfig.removeRequestHeaders[i];
        delete request.Headers[header];
        delete request.Headers[header.toLowerCase()];
        delete request.Headers[header.toUpperCase()];
    }
    
    // Add security context headers
    request.SetHeaders['X-Caremate-Transform-Applied'] = 'true';
    request.SetHeaders['X-Caremate-Request-ID'] = 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    request.SetHeaders['X-Caremate-Timestamp'] = new Date().toISOString();
    
    // Log sanitized request (for debugging)
    var sanitizedURI = sanitizeData(request.RequestURI);
    log("CareMate Transform: Processed request - Method: " + method + ", Path: " + sanitizedURI);
    
    return CareMateTransform.ReturnData(request, {});
});

// Response transformation middleware
CareMateTransform.NewProcessResponse(function(response, session, config) {
    log("CareMate Transform: Processing response transformation");
    
    // Remove sensitive headers from response
    for (var i = 0; i < transformConfig.removeResponseHeaders.length; i++) {
        var header = transformConfig.removeResponseHeaders[i];
        delete response.Headers[header];
        delete response.Headers[header.toLowerCase()];
        delete response.Headers[header.toUpperCase()];
    }
    
    // Add security headers to response
    for (var headerName in transformConfig.addResponseHeaders) {
        response.Headers[headerName] = transformConfig.addResponseHeaders[headerName];
    }
    
    // Add CORS headers if not already present
    if (!response.Headers["Access-Control-Allow-Origin"]) {
        response.Headers["Access-Control-Allow-Origin"] = "*"; // Will be overridden by CORS config
    }
    
    // Sanitize response body for logging (don't modify actual response)
    if (response.Body) {
        var sanitizedBody = sanitizeData(response.Body);
        log("CareMate Transform: Response processed - Status: " + response.Code + ", Body length: " + (response.Body ? response.Body.length : 0));
    }
    
    // Add response metadata headers
    response.Headers['X-Caremate-Response-Time'] = new Date().toISOString();
    response.Headers['X-Caremate-Gateway'] = 'tyk-caremate';
    
    return CareMateTransform.ReturnData(response, {});
});

// Export the middleware
CareMateTransform;
