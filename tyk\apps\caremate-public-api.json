{"name": "CareMate Public API", "api_id": "caremate-public", "org_id": "default", "definition": {"location": "header", "key": "version"}, "use_keyless": true, "global_rate_limit": {"rate": 100, "per": 60}, "global_rate_limit_disabled": false, "enable_detailed_recording": true, "tags": ["caremate", "public", "keyless"], "CORS": {"enable": true, "allowed_origins": ["http://localhost:3000", "http://localhost:3001"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "allowed_headers": ["Origin", "Accept", "Content-Type", "X-Requested-With", "Authorization"], "exposed_headers": [], "allow_credentials": true, "max_age": 24, "options_passthrough": false, "debug": false}, "response_processors": [{"name": "header_injector", "options": {"add_headers": {"X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Strict-Transport-Security": "max-age=31536000; includeSubDomains", "Referrer-Policy": "strict-origin-when-cross-origin"}, "remove_headers": ["Server", "X-Powered-By"]}}], "request_size_limit": 524288, "custom_middleware": {"driver": "otto", "pre": [{"name": "CareMateSecurityMiddleware", "path": "./middleware/caremate-security.js", "require_session": false, "raw_body_only": false}, {"name": "CareMateRateLimiter", "path": "./middleware/caremate-rate-limiter.js", "require_session": false, "raw_body_only": false}, {"name": "CareMateTransform", "path": "./middleware/caremate-transform.js", "require_session": false, "raw_body_only": false}], "post": [{"name": "CareMatePublic", "path": "./middleware/caremate-public.js", "require_session": false, "raw_body_only": false}], "response": [{"name": "CareMateTransform", "path": "./middleware/caremate-transform.js", "require_session": false, "raw_body_only": false}]}, "version_data": {"not_versioned": true, "versions": {"Default": {"name": "<PERSON><PERSON><PERSON>"}}}, "proxy": {"listen_path": "/caremate/api/", "target_url": "http://************:3001/api/", "strip_listen_path": true, "preserve_host_header": false, "disable_strip_slash": false, "check_host_against_uptime_tests": false}}