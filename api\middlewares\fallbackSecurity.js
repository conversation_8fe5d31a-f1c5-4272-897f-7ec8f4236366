const { status: httpStatus } = require("http-status");
const rateLimit = require("express-rate-limit");
const { ApiError } = require("../helpers/api.helper");
const config = require("../config/config");

/**
 * Fallback Security Middleware
 * Provides security when requests bypass Tyk Gateway or when Tyk headers are invalid
 */

// Configuration for fallback security
const FALLBACK_CONFIG = {
  // Allowed direct access IPs (very restrictive)
  allowedDirectIPs: [
    "127.0.0.1",
    "::1"
  ],
  
  // Endpoints that are allowed without Tyk (emergency access)
  emergencyEndpoints: [
    "/health",
    "/api/health",
    "/api/docs"
  ],
  
  // Rate limiting for direct access (very strict)
  directAccessRateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Very low limit for direct access
    message: {
      status: false,
      message: "Direct API access rate limit exceeded. Please use the gateway.",
      error: "DIRECT_ACCESS_RATE_LIMIT"
    }
  }
};

/**
 * Rate limiter for direct API access (very restrictive)
 */
const directAccessLimiter = rateLimit(FALLBACK_CONFIG.directAccessRateLimit);

/**
 * Checks if the request is coming through Tyk Gateway
 * @param {Object} req - Express request object
 * @returns {boolean} - True if request has valid Tyk headers
 */
const hasTykHeaders = (req) => {
  const requiredHeaders = [
    'x-caremate-security-validated',
    'x-caremate-timestamp',
    'x-caremate-signature'
  ];
  
  return requiredHeaders.every(header => req.headers[header]);
};

/**
 * Checks if the endpoint is allowed for emergency access
 * @param {string} path - Request path
 * @returns {boolean} - True if endpoint allows direct access
 */
const isEmergencyEndpoint = (path) => {
  return FALLBACK_CONFIG.emergencyEndpoints.some(endpoint => 
    path.startsWith(endpoint)
  );
};

/**
 * Validates if direct API access is allowed
 * @param {Object} req - Express request object
 * @returns {boolean} - True if direct access is allowed
 */
const isDirectAccessAllowed = (req) => {
  // Get client IP
  let clientIP = req.ip || req.connection.remoteAddress;
  if (req.headers['x-forwarded-for']) {
    clientIP = req.headers['x-forwarded-for'].split(',')[0].trim();
  } else if (req.headers['x-real-ip']) {
    clientIP = req.headers['x-real-ip'];
  }
  
  // Remove IPv6 prefix if present
  if (clientIP && clientIP.startsWith('::ffff:')) {
    clientIP = clientIP.substring(7);
  }
  
  // Check if IP is in allowed list for direct access
  return FALLBACK_CONFIG.allowedDirectIPs.includes(clientIP);
};

/**
 * Main fallback security middleware
 */
const fallbackSecurity = (req, res, next) => {
  // Skip if in custom auth mode (not using Tyk)
  if (config.auth.mode !== 'tyk') {
    return next();
  }
  
  const requestPath = req.path;
  
  // Check if request has Tyk headers (normal flow)
  if (hasTykHeaders(req)) {
    // Request came through Tyk Gateway - proceed normally
    return next();
  }
  
  // Request is bypassing Tyk Gateway - apply fallback security
  console.warn(`SECURITY WARNING: Direct API access detected for ${requestPath} from ${req.ip}`);
  
  // Check if this is an emergency endpoint
  if (isEmergencyEndpoint(requestPath)) {
    // Allow emergency endpoints but with rate limiting
    return directAccessLimiter(req, res, next);
  }
  
  // Check if direct access is allowed from this IP
  if (!isDirectAccessAllowed(req)) {
    console.error(`SECURITY ALERT: Unauthorized direct API access from ${req.ip} to ${requestPath}`);
    return res.status(httpStatus.FORBIDDEN).json({
      status: false,
      message: "Direct API access not allowed. Please use the gateway.",
      error: "DIRECT_ACCESS_FORBIDDEN",
      gateway_url: config.tyk?.url || "http://localhost:8181",
      timestamp: new Date().toISOString()
    });
  }
  
  // Apply strict rate limiting for allowed direct access
  directAccessLimiter(req, res, (err) => {
    if (err) {
      return res.status(httpStatus.TOO_MANY_REQUESTS).json({
        status: false,
        message: "Too many direct API requests. Please use the gateway.",
        error: "DIRECT_ACCESS_RATE_LIMITED",
        gateway_url: config.tyk?.url || "http://localhost:8181",
        timestamp: new Date().toISOString()
      });
    }
    
    // Add warning headers for direct access
    res.set({
      'X-CareMate-Warning': 'Direct API access detected',
      'X-CareMate-Recommendation': 'Use gateway for better security',
      'X-CareMate-Gateway-URL': config.tyk?.url || "http://localhost:8181"
    });
    
    console.warn(`SECURITY WARNING: Allowing limited direct access from ${req.ip} to ${requestPath}`);
    next();
  });
};

/**
 * Emergency access middleware for critical endpoints
 * Provides minimal access when gateway is down
 */
const emergencyAccess = (req, res, next) => {
  // Only allow from localhost
  const clientIP = req.ip || req.connection.remoteAddress;
  if (!['127.0.0.1', '::1', '::ffff:127.0.0.1'].includes(clientIP)) {
    return res.status(httpStatus.FORBIDDEN).json({
      status: false,
      message: "Emergency access only allowed from localhost",
      error: "EMERGENCY_ACCESS_FORBIDDEN"
    });
  }
  
  // Add emergency access headers
  res.set({
    'X-CareMate-Emergency-Access': 'true',
    'X-CareMate-Warning': 'Gateway bypass detected - emergency mode'
  });
  
  next();
};

/**
 * Security monitoring for fallback scenarios
 */
const monitorFallbackSecurity = (req, res, next) => {
  // Log security events for monitoring
  if (config.auth.mode === 'tyk' && !hasTykHeaders(req)) {
    const securityEvent = {
      timestamp: new Date().toISOString(),
      type: 'GATEWAY_BYPASS_ATTEMPT',
      ip: req.ip,
      path: req.path,
      method: req.method,
      userAgent: req.get('User-Agent'),
      headers: {
        'x-forwarded-for': req.get('x-forwarded-for'),
        'x-real-ip': req.get('x-real-ip'),
        'authorization': req.get('authorization') ? '[PRESENT]' : '[MISSING]'
      }
    };
    
    console.log('SECURITY EVENT:', JSON.stringify(securityEvent));
    
    // In production, send to security monitoring system
    // sendToSecurityMonitoring(securityEvent);
  }
  
  next();
};

module.exports = {
  fallbackSecurity,
  emergencyAccess,
  monitorFallbackSecurity,
  hasTykHeaders,
  isDirectAccessAllowed
};
