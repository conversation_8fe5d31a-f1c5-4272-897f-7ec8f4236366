{"description": "CareMate IP Whitelist Configuration", "environments": {"development": {"allowed_ips": ["127.0.0.1", "::1", "localhost", "***********/24", "10.0.0.0/8", "**********/12"], "blocked_ips": ["0.0.0.0/0"], "enable_whitelist": false, "enable_blacklist": true}, "staging": {"allowed_ips": ["127.0.0.1", "::1", "***********/24", "10.0.0.0/16"], "blocked_ips": [], "enable_whitelist": true, "enable_blacklist": true}, "production": {"allowed_ips": ["127.0.0.1", "::1"], "blocked_ips": ["0.0.0.0/1", "*********/1"], "enable_whitelist": true, "enable_blacklist": true}}, "security_rules": {"max_request_size": 1048576, "rate_limit_per_ip": {"requests": 100, "window_seconds": 60}, "suspicious_patterns": ["(?i)(union|select|insert|delete|drop|create|alter|exec|script)", "(?i)(<script|javascript:|vbscript:|onload=|onerror=)", "(?i)(\\.\\.[\\/\\\\]|\\.\\.%2f|\\.\\.%5c)", "(?i)(cmd\\.exe|powershell|bash|sh|/bin/)"], "blocked_user_agents": ["sqlmap", "nikto", "nmap", "masscan", "nessus", "openvas", "burp", "w3af"], "required_headers": ["User-Agent", "Accept"]}}