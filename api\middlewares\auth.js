const passport = require('../config/passport');
const { status: httpStatus } = require("http-status");
const crypto = require("crypto");
const { ApiError } = require("../helpers/api.helper");
const config = require("../config/config");
const { Identity } = require("../models");

/**
 * Simple header signature validation to prevent spoofing
 * Uses JWT secret + suffix to create HMAC signature
 */
const HEADER_SIGNATURE_SECRET = config.jwt.secret + "_tyk_headers";

/**
 * Validates Tyk header signature to prevent spoofing
 * @param {Object} req - Express request object
 * @returns {boolean} - True if signature is valid
 */
const validateTykSignature = (req) => {
  const signature = req.headers['x-caremate-signature'];
  if (!signature) return false;

  try {
    // Create signature payload from critical headers
    const payload = {
      identityId: req.headers['x-caremate-identity-id'],
      permissions: req.headers['x-caremate-permissions'],
      authorized: req.headers['x-caremate-authorized'],
      timestamp: req.headers['x-caremate-timestamp']
    };

    // Generate expected signature
    const expectedSignature = crypto
      .createHmac('sha256', HEADER_SIGNATURE_SECRET)
      .update(JSON.stringify(payload))
      .digest('hex');

    // Compare signatures using timing-safe comparison
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    return false;
  }
};

/**
 * Simple Tyk header validation with anti-spoofing
 * @param {Object} req - Express request object
 * @param {Array} requiredRights - Required permissions
 * @returns {Promise<Object>} - Identity object with permissions
 */
const validateTykHeaders = async (req, requiredRights) => {
  // ANTI-SPOOFING: Validate header signature to prevent tampering
  if (!validateTykSignature(req)) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid Tyk header signature - headers may be spoofed");
  }

  // Extract and validate core authentication data
  const identityId = req.headers['x-caremate-identity-id'];
  const permissions = req.headers['x-caremate-permissions'];
  const authorized = req.headers['x-caremate-authorized'];

  if (!identityId || !permissions || authorized !== 'true') {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or missing Tyk authentication headers");
  }

  // Validate permissions format
  let parsedPermissions = [];
  try {
    parsedPermissions = JSON.parse(permissions);
    if (!Array.isArray(parsedPermissions)) {
      throw new Error("Permissions must be an array");
    }
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid permissions format from Tyk Gateway");
  }

  // Validate identity exists in database (skip for public requests)
  let identity = null;
  if (identityId !== 'public') {
    identity = await Identity.findByPk(identityId);
    if (!identity) {
      throw new ApiError(httpStatus.UNAUTHORIZED, "Identity not found in database");
    }
  } else {
    // Create a mock identity object for public requests
    identity = {
      id: 'public',
      email: 'public',
      permissions: parsedPermissions
    };
  }

  // Add permissions to identity object
  identity.permissions = parsedPermissions;

  // Check required permissions
  if (requiredRights.length > 0) {
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      parsedPermissions.includes(requiredRight)
    );

    if (!hasRequiredRights && req.params.identityId !== identityId) {
      throw new ApiError(httpStatus.FORBIDDEN, "Insufficient permissions for this operation");
    }
  }

  return identity;
};

// The verifyCallback for custom JWT authentication
const verifyCallback = (req, resolve, reject, requiredRights) => async (err, identity, info) => {
  if (err || info || !identity) {
    return reject(
      new ApiError(httpStatus.UNAUTHORIZED, "Please authenticate")
    );
  }

  req.identity = identity;

  if (requiredRights.length) {
    // Get permissions directly from the JWT token (added by passport strategy)
    const identityRights = identity.permissions || [];

    if (identityRights.length === 0) {
      return reject(
        new ApiError(httpStatus.FORBIDDEN, "No permissions assigned to identity")
      );
    }

    // Check required rights
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      identityRights.includes(requiredRight)
    );

    if (
      !hasRequiredRights &&
      req.params.identityId !== identity.identity_id
    ) {
      return reject(new ApiError(httpStatus.FORBIDDEN, "Unauthorized access of identity"));
    }
  }

  resolve();
};

/**
 * Main authentication middleware that supports both custom and Tyk modes
 * @param {...string} requiredRights - Required permissions for the endpoint
 * @returns {Function} - Express middleware function
 */
const auth = (...requiredRights) =>
  async (req, res, next) => {
    try {
      // Check authentication mode from configuration
      if (config.auth.mode === 'tyk') {
        // Tyk Gateway mode - validate headers set by Tyk
        const identity = await validateTykHeaders(req, requiredRights);
        req.identity = identity;
        return next();
      } else {
        // Custom mode - use existing JWT authentication
        return new Promise((resolve, reject) => {
          passport.authenticate(
            "custom",
            { session: false },
            verifyCallback(req, resolve, reject, requiredRights)
          )(req, res, next);
        })
          .then(() => next())
          .catch((err) => next(err));
      }
    } catch (error) {
      return next(error);
    }
  };

// Export auth as default function for backward compatibility
module.exports = auth;
