const passport = require('../config/passport');
const { status: httpStatus } = require("http-status");
const crypto = require("crypto");
const jwt = require("jsonwebtoken");
const { ApiError } = require("../helpers/api.helper");
const config = require("../config/config");
const { Identity } = require("../models");

/**
 * Security configuration for Tyk header validation
 */
const TYK_SECURITY_CONFIG = {
  // Secret for header signature validation (should match Tyk middleware)
  headerSignatureSecret: config.jwt.secret + "_tyk_headers",

  // Allowed source IPs for Tyk gateway
  allowedTykIPs: [
    "127.0.0.1",
    "::1",
    "************", // Your Tyk gateway IP
    "localhost"
  ],

  // Maximum age for Tyk headers (in seconds)
  maxHeaderAge: 300, // 5 minutes

  // Required Tyk headers
  requiredHeaders: [
    'x-caremate-identity-id',
    'x-caremate-permissions',
    'x-caremate-authorized',
    'x-caremate-security-validated',
    'x-caremate-timestamp'
  ]
};

/**
 * Validates that the request is actually coming from Tyk Gateway
 * @param {Object} req - Express request object
 * @returns {boolean} - True if request source is valid
 */
const validateRequestSource = (req) => {
  // Check if request has Tyk security validation header
  const securityValidated = req.headers['x-caremate-security-validated'];
  if (securityValidated !== 'true') {
    return false;
  }

  // Get client IP (considering proxy headers)
  let clientIP = req.ip || req.connection.remoteAddress;
  if (req.headers['x-forwarded-for']) {
    clientIP = req.headers['x-forwarded-for'].split(',')[0].trim();
  } else if (req.headers['x-real-ip']) {
    clientIP = req.headers['x-real-ip'];
  }

  // Remove IPv6 prefix if present
  if (clientIP && clientIP.startsWith('::ffff:')) {
    clientIP = clientIP.substring(7);
  }

  // Check if IP is in allowed list
  return TYK_SECURITY_CONFIG.allowedTykIPs.includes(clientIP);
};

/**
 * Validates the timestamp in Tyk headers to prevent replay attacks
 * @param {string} timestamp - ISO timestamp from Tyk headers
 * @returns {boolean} - True if timestamp is valid and recent
 */
const validateHeaderTimestamp = (timestamp) => {
  if (!timestamp) return false;

  try {
    const headerTime = new Date(timestamp).getTime();
    const currentTime = Date.now();
    const maxAge = TYK_SECURITY_CONFIG.maxHeaderAge * 1000; // Convert to milliseconds

    // Check if timestamp is not too old and not in the future
    return (currentTime - headerTime) <= maxAge && headerTime <= (currentTime + 60000); // Allow 1 minute clock skew
  } catch (error) {
    return false;
  }
};

/**
 * Validates the signature of Tyk headers to prevent tampering
 * @param {Object} req - Express request object
 * @returns {boolean} - True if signature is valid
 */
const validateHeaderSignature = (req) => {
  const signature = req.headers['x-caremate-signature'];
  if (!signature) return false;

  try {
    // Create signature payload from critical headers
    const payload = {
      identityId: req.headers['x-caremate-identity-id'],
      permissions: req.headers['x-caremate-permissions'],
      authorized: req.headers['x-caremate-authorized'],
      timestamp: req.headers['x-caremate-timestamp']
    };

    // Generate expected signature
    const expectedSignature = crypto
      .createHmac('sha256', TYK_SECURITY_CONFIG.headerSignatureSecret)
      .update(JSON.stringify(payload))
      .digest('hex');

    // Compare signatures using timing-safe comparison
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    return false;
  }
};

/**
 * Validates JWT token directly as fallback security measure
 * @param {Object} req - Express request object
 * @returns {Object|null} - Decoded JWT payload or null if invalid
 */
const validateJWTFallback = (req) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    return jwt.verify(token, config.jwt.secret);
  } catch (error) {
    return null;
  }
};

/**
 * Enhanced Tyk header validation with security checks
 * @param {Object} req - Express request object
 * @param {Array} requiredRights - Required permissions
 * @returns {Promise<Object>} - Identity object with permissions
 */
const validateTykHeaders = async (req, requiredRights) => {
  // SECURITY LAYER 1: Validate request source
  if (!validateRequestSource(req)) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Request not from authorized Tyk Gateway");
  }

  // SECURITY LAYER 2: Check for required Tyk headers
  const missingHeaders = TYK_SECURITY_CONFIG.requiredHeaders.filter(
    header => !req.headers[header]
  );
  if (missingHeaders.length > 0) {
    throw new ApiError(httpStatus.UNAUTHORIZED, `Missing required Tyk headers: ${missingHeaders.join(', ')}`);
  }

  // SECURITY LAYER 3: Validate header timestamp to prevent replay attacks
  const timestamp = req.headers['x-caremate-timestamp'];
  if (!validateHeaderTimestamp(timestamp)) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or expired Tyk header timestamp");
  }

  // SECURITY LAYER 4: Validate header signature to prevent tampering
  if (!validateHeaderSignature(req)) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid Tyk header signature - possible tampering detected");
  }

  // SECURITY LAYER 5: Extract and validate core authentication data
  const identityId = req.headers['x-caremate-identity-id'];
  const permissions = req.headers['x-caremate-permissions'];
  const authorized = req.headers['x-caremate-authorized'];

  if (!identityId || !permissions || authorized !== 'true') {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid or missing Tyk authentication headers");
  }

  // SECURITY LAYER 6: Validate permissions format
  let parsedPermissions = [];
  try {
    parsedPermissions = JSON.parse(permissions);
    if (!Array.isArray(parsedPermissions)) {
      throw new Error("Permissions must be an array");
    }
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Invalid permissions format from Tyk Gateway");
  }

  // SECURITY LAYER 7: Validate identity exists in database (skip for public requests)
  let identity = null;
  if (identityId !== 'public') {
    identity = await Identity.findByPk(identityId);
    if (!identity) {
      // FALLBACK SECURITY: Try to validate JWT token directly
      const jwtPayload = validateJWTFallback(req);
      if (!jwtPayload || jwtPayload.sub !== identityId) {
        throw new ApiError(httpStatus.UNAUTHORIZED, "Identity not found in database and JWT validation failed");
      }

      // Create identity from JWT if database lookup fails but JWT is valid
      identity = {
        id: jwtPayload.sub,
        email: jwtPayload.email || 'unknown',
        permissions: jwtPayload.permissions || []
      };
    }
  } else {
    // Create a mock identity object for public requests
    identity = {
      id: 'public',
      email: 'public',
      permissions: parsedPermissions
    };
  }

  // SECURITY LAYER 8: Cross-validate permissions from headers vs database/JWT
  if (identity.id !== 'public') {
    // For non-public users, ensure permissions match what's expected
    const dbPermissions = identity.permissions || [];
    const headerPermissions = parsedPermissions;

    // Check if header permissions are a subset of database permissions
    const invalidPermissions = headerPermissions.filter(
      perm => !dbPermissions.includes(perm)
    );

    if (invalidPermissions.length > 0) {
      throw new ApiError(httpStatus.FORBIDDEN, `Invalid permissions in headers: ${invalidPermissions.join(', ')}`);
    }
  }

  // Add validated permissions to identity object
  identity.permissions = parsedPermissions;

  // SECURITY LAYER 9: Check required permissions
  if (requiredRights.length > 0) {
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      parsedPermissions.includes(requiredRight)
    );

    if (!hasRequiredRights && req.params.identityId !== identityId) {
      throw new ApiError(httpStatus.FORBIDDEN, "Insufficient permissions for this operation");
    }
  }

  // SECURITY LAYER 10: Add security audit information
  identity.securityValidation = {
    source: 'tyk-gateway',
    timestamp: timestamp,
    validated: true,
    layers: [
      'source-validation',
      'header-presence',
      'timestamp-validation',
      'signature-validation',
      'data-validation',
      'permission-validation',
      'database-validation',
      'cross-validation',
      'authorization-validation'
    ]
  };

  return identity;
};

// The verifyCallback for custom JWT authentication
const verifyCallback = (req, resolve, reject, requiredRights) => async (err, identity, info) => {
  if (err || info || !identity) {
    return reject(
      new ApiError(httpStatus.UNAUTHORIZED, "Please authenticate")
    );
  }

  req.identity = identity;

  if (requiredRights.length) {
    // Get permissions directly from the JWT token (added by passport strategy)
    const identityRights = identity.permissions || [];

    if (identityRights.length === 0) {
      return reject(
        new ApiError(httpStatus.FORBIDDEN, "No permissions assigned to identity")
      );
    }

    // Check required rights
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      identityRights.includes(requiredRight)
    );

    if (
      !hasRequiredRights &&
      req.params.identityId !== identity.identity_id
    ) {
      return reject(new ApiError(httpStatus.FORBIDDEN, "Unauthorized access of identity"));
    }
  }

  resolve();
};

/**
 * Main authentication middleware that supports both custom and Tyk modes
 * @param {...string} requiredRights - Required permissions for the endpoint
 * @returns {Function} - Express middleware function
 */
const auth = (...requiredRights) =>
  async (req, res, next) => {
    try {
      // Check authentication mode from configuration
      if (config.auth.mode === 'tyk') {
        // Tyk Gateway mode - validate headers set by Tyk
        const identity = await validateTykHeaders(req, requiredRights);
        req.identity = identity;
        return next();
      } else {
        // Custom mode - use existing JWT authentication
        return new Promise((resolve, reject) => {
          passport.authenticate(
            "custom",
            { session: false },
            verifyCallback(req, resolve, reject, requiredRights)
          )(req, res, next);
        })
          .then(() => next())
          .catch((err) => next(err));
      }
    } catch (error) {
      return next(error);
    }
  };

// Export auth as default function for backward compatibility
module.exports = auth;
