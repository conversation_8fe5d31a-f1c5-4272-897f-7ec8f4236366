{"description": "Mutual TLS Configuration for CareMate Gateway", "environments": {"development": {"enabled": false, "require_client_cert": false, "verify_client_cert": false, "client_ca_file": "", "server_cert_file": "", "server_key_file": ""}, "staging": {"enabled": true, "require_client_cert": true, "verify_client_cert": true, "client_ca_file": "/opt/tyk-gateway/certs/staging/client-ca.pem", "server_cert_file": "/opt/tyk-gateway/certs/staging/server.crt", "server_key_file": "/opt/tyk-gateway/certs/staging/server.key", "allowed_client_certs": ["staging-api-client.crt", "staging-admin-client.crt"]}, "production": {"enabled": true, "require_client_cert": true, "verify_client_cert": true, "client_ca_file": "/opt/tyk-gateway/certs/production/client-ca.pem", "server_cert_file": "/opt/tyk-gateway/certs/production/server.crt", "server_key_file": "/opt/tyk-gateway/certs/production/server.key", "allowed_client_certs": ["production-api-client.crt", "production-admin-client.crt", "production-integration-client.crt"], "certificate_pinning": {"enabled": true, "pins": ["sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=", "sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB="]}}}, "certificate_validation": {"check_expiry": true, "check_revocation": true, "max_cert_chain_length": 3, "allowed_key_usages": ["digital_signature", "key_encipherment", "client_auth"], "required_extensions": ["key_usage", "extended_key_usage"]}, "upstream_certificates": {"api_server": {"cert_file": "/opt/tyk-gateway/certs/upstream/api-server.crt", "key_file": "/opt/tyk-gateway/certs/upstream/api-server.key", "ca_file": "/opt/tyk-gateway/certs/upstream/ca.pem", "verify_ssl": true, "skip_verify": false}}, "certificate_rotation": {"enabled": true, "check_interval_hours": 24, "renewal_threshold_days": 30, "auto_reload": true}}