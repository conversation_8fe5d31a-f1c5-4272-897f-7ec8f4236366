{"log_level": "info", "listen_port": 8181, "secret": "352d20ee67be67f6340b4c0605b045c8", "template_path": "/opt/tyk-gateway/templates", "tyk_js_path": "/opt/tyk-gateway/js/tyk.js", "middleware_path": "/opt/tyk-gateway/middleware", "use_db_app_configs": false, "app_path": "/opt/tyk-gateway/apps/", "storage": {"type": "redis", "host": "redis", "port": 6379, "username": "", "password": "", "database": 0, "optimisation_max_idle": 2000, "optimisation_max_active": 4000}, "enable_analytics": true, "analytics_config": {"type": "file", "csv_dir": "/opt/tyk-gateway/analytics", "enable_detailed_recording": true, "ignored_ips": []}, "health_check": {"enable_health_checks": true, "health_check_value_timeouts": 60}, "enable_non_transactional_rate_limiter": true, "enable_sentinel_rate_limiter": true, "enable_redis_rolling_limiter": true, "allow_master_keys": false, "policies": {"policy_source": "file", "policy_path": "/opt/tyk-gateway/policies"}, "hash_keys": true, "close_connections": false, "http_server_options": {"enable_websockets": true, "certificates": [], "ssl_insecure_skip_verify": false, "use_ssl": false, "server_name": "", "min_version": 771, "flush_interval": 0, "skip_url_cleaning": false, "skip_target_path_escaping": false, "ssl_ciphers": [], "override_defaults": false, "read_timeout": 0, "write_timeout": 0}, "allow_insecure_configs": false, "coprocess_options": {"enable_coprocess": true, "coprocess_grpc_server": ""}, "enable_bundle_downloader": true, "bundle_base_url": "", "global_session_lifetime": 100, "force_global_session_lifetime": false, "max_idle_connections_per_host": 500, "enable_jsvm": true, "security": {"private_certificate_encoding_secret": "$TYK_SECURITY_SECRET", "control_api_use_ssl": false, "pinned_public_keys": {}, "certificates": {}}, "enable_key_logging": false, "ssl_force_common_name_check": false, "enforce_org_data_age": true, "enforce_org_data_detail_logging": false, "enforce_org_quotas": true, "experimental_process_org_off_thread": true, "DRL_notification_frequency": 0, "DRL_enable_sentinel_rate_limiter": true, "DRL_threshold": 0, "monitor": {"enable_trigger_monitors": false, "configuration": {}, "global_trigger_limit": 80.0, "monitor_user_keys": false, "monitor_org_keys": false}}