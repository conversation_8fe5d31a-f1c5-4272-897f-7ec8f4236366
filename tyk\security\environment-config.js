// Environment-specific security configuration for CareMate Tyk Gateway
// This file provides different security levels based on the deployment environment

var EnvironmentConfig = {
    // Development environment - relaxed security for testing
    development: {
        security: {
            enableIPWhitelist: false,
            enableIPBlacklist: false,
            allowedIPs: [
                "127.0.0.1",
                "::1",
                "localhost",
                "***********/16",
                "10.0.0.0/8",
                "**********/12"
            ],
            blockedIPs: [],
            maxRequestSize: 2097152, // 2MB
            rateLimitPerIP: {
                requests: 1000,
                windowSeconds: 60
            },
            enableWAF: false,
            logLevel: "debug"
        },
        cors: {
            allowedOrigins: ["*"],
            allowCredentials: true,
            maxAge: 24
        },
        headers: {
            strictTransportSecurity: false,
            contentSecurityPolicy: false
        }
    },

    // Staging environment - moderate security
    staging: {
        security: {
            enableIPWhitelist: true,
            enableIPBlacklist: true,
            allowedIPs: [
                "127.0.0.1",
                "::1",
                "***********/24",
                "10.0.0.0/16"
            ],
            blockedIPs: [
                // Add known malicious IPs
            ],
            maxRequestSize: 1048576, // 1MB
            rateLimitPerIP: {
                requests: 500,
                windowSeconds: 60
            },
            enableWAF: true,
            logLevel: "info"
        },
        cors: {
            allowedOrigins: [
                "https://staging.caremate.com",
                "https://staging-admin.caremate.com"
            ],
            allowCredentials: true,
            maxAge: 24
        },
        headers: {
            strictTransportSecurity: true,
            contentSecurityPolicy: true
        }
    },

    // Production environment - maximum security
    production: {
        security: {
            enableIPWhitelist: true,
            enableIPBlacklist: true,
            allowedIPs: [
                "127.0.0.1",
                "::1"
                // Add production server IPs and load balancer IPs
            ],
            blockedIPs: [
                // Add comprehensive list of malicious IPs
                "0.0.0.0/1",
                "*********/1"
            ],
            maxRequestSize: 524288, // 512KB
            rateLimitPerIP: {
                requests: 100,
                windowSeconds: 60
            },
            enableWAF: true,
            logLevel: "warn"
        },
        cors: {
            allowedOrigins: [
                "https://caremate.com",
                "https://admin.caremate.com",
                "https://app.caremate.com"
            ],
            allowCredentials: true,
            maxAge: 86400 // 24 hours
        },
        headers: {
            strictTransportSecurity: true,
            contentSecurityPolicy: true,
            enableHSTS: true,
            enableCSP: true
        }
    }
};

// Function to get configuration based on environment
function getSecurityConfig(environment) {
    environment = environment || "development";
    return EnvironmentConfig[environment] || EnvironmentConfig.development;
}

// Function to apply environment-specific security settings
function applySecurityConfig(request, environment) {
    var config = getSecurityConfig(environment);
    
    // Set security headers based on environment
    if (config.headers.strictTransportSecurity) {
        request.SetHeaders['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
    }
    
    if (config.headers.contentSecurityPolicy) {
        request.SetHeaders['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:";
    }
    
    // Additional security headers
    request.SetHeaders['X-Content-Type-Options'] = 'nosniff';
    request.SetHeaders['X-Frame-Options'] = 'DENY';
    request.SetHeaders['X-XSS-Protection'] = '1; mode=block';
    request.SetHeaders['Referrer-Policy'] = 'strict-origin-when-cross-origin';
    request.SetHeaders['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()';
    
    return config;
}

// Export for use in other middleware
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getSecurityConfig: getSecurityConfig,
        applySecurityConfig: applySecurityConfig,
        EnvironmentConfig: EnvironmentConfig
    };
}
