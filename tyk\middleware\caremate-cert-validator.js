// CareMate Certificate Validation Middleware for Tyk Gateway
// Implements client certificate validation and mutual TLS security

var CareMateClientCertValidator = new TykJS.TykMiddleware.NewMiddleware({});

// Certificate validation configuration
var certConfig = {
    // Environment-specific settings
    environment: "development", // Set via environment variable
    
    // Development settings (relaxed)
    development: {
        enabled: false,
        requireClientCert: false,
        verifyClientCert: false,
        logLevel: "debug"
    },
    
    // Staging settings (moderate)
    staging: {
        enabled: true,
        requireClientCert: true,
        verifyClientCert: true,
        allowedClientCerts: [
            "staging-api-client",
            "staging-admin-client"
        ],
        logLevel: "info"
    },
    
    // Production settings (strict)
    production: {
        enabled: true,
        requireClientCert: true,
        verifyClientCert: true,
        allowedClientCerts: [
            "production-api-client",
            "production-admin-client",
            "production-integration-client"
        ],
        certificatePinning: {
            enabled: true,
            pins: [
                "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=",
                "sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB="
            ]
        },
        logLevel: "warn"
    },
    
    // Certificate validation rules
    validation: {
        checkExpiry: true,
        checkRevocation: false, // Set to true when CRL/OCSP is available
        maxCertChainLength: 3,
        allowedKeyUsages: [
            "digital_signature",
            "key_encipherment",
            "client_auth"
        ],
        requiredExtensions: [
            "key_usage",
            "extended_key_usage"
        ]
    },
    
    // Trusted Certificate Authorities (fingerprints)
    trustedCAs: [
        "sha256/CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC=", // Internal CA
        "sha256/DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD="  // Backup CA
    ]
};

// Helper function to get current environment config
function getCurrentConfig() {
    var env = certConfig.environment || "development";
    return certConfig[env] || certConfig.development;
}

// Helper function to extract certificate information from headers
function extractCertInfo(request) {
    // Tyk sets these headers when client certificates are present
    var certSubject = request.Headers["x-tyk-client-cert-subject"];
    var certIssuer = request.Headers["x-tyk-client-cert-issuer"];
    var certFingerprint = request.Headers["x-tyk-client-cert-fingerprint"];
    var certSerial = request.Headers["x-tyk-client-cert-serial"];
    var certNotBefore = request.Headers["x-tyk-client-cert-not-before"];
    var certNotAfter = request.Headers["x-tyk-client-cert-not-after"];
    
    if (!certSubject && !certFingerprint) {
        return null;
    }
    
    return {
        subject: certSubject,
        issuer: certIssuer,
        fingerprint: certFingerprint,
        serial: certSerial,
        notBefore: certNotBefore,
        notAfter: certNotAfter
    };
}

// Helper function to validate certificate expiry
function validateCertExpiry(certInfo) {
    if (!certConfig.validation.checkExpiry || !certInfo.notAfter) {
        return { valid: true };
    }
    
    try {
        var expiryDate = new Date(certInfo.notAfter);
        var now = new Date();
        
        if (expiryDate <= now) {
            return {
                valid: false,
                reason: "Certificate has expired",
                expiry: certInfo.notAfter
            };
        }
        
        // Check if certificate expires within 7 days (warning)
        var sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
        if (expiryDate <= sevenDaysFromNow) {
            log("CareMate Cert Validator: WARNING - Certificate expires soon: " + certInfo.notAfter);
        }
        
        return { valid: true };
    } catch (error) {
        return {
            valid: false,
            reason: "Invalid certificate date format",
            error: error.message
        };
    }
}

// Helper function to validate certificate against allowed list
function validateAllowedCert(certInfo, config) {
    if (!config.allowedClientCerts || config.allowedClientCerts.length === 0) {
        return { valid: true };
    }
    
    if (!certInfo.subject) {
        return {
            valid: false,
            reason: "Certificate subject not available"
        };
    }
    
    // Extract CN from subject
    var cnMatch = certInfo.subject.match(/CN=([^,]+)/);
    var commonName = cnMatch ? cnMatch[1].trim() : "";
    
    for (var i = 0; i < config.allowedClientCerts.length; i++) {
        if (commonName.indexOf(config.allowedClientCerts[i]) !== -1) {
            return { valid: true };
        }
    }
    
    return {
        valid: false,
        reason: "Certificate not in allowed list",
        commonName: commonName,
        allowedCerts: config.allowedClientCerts
    };
}

// Helper function to validate certificate pinning
function validateCertificatePinning(certInfo, config) {
    if (!config.certificatePinning || !config.certificatePinning.enabled) {
        return { valid: true };
    }
    
    if (!certInfo.fingerprint) {
        return {
            valid: false,
            reason: "Certificate fingerprint not available for pinning validation"
        };
    }
    
    var pins = config.certificatePinning.pins || [];
    for (var i = 0; i < pins.length; i++) {
        if (certInfo.fingerprint === pins[i]) {
            return { valid: true };
        }
    }
    
    return {
        valid: false,
        reason: "Certificate fingerprint not in pinned list",
        fingerprint: certInfo.fingerprint,
        pinnedFingerprints: pins
    };
}

// Main certificate validation middleware
CareMateClientCertValidator.NewProcessRequest(function(request, session, config) {
    var currentConfig = getCurrentConfig();
    
    log("CareMate Cert Validator: Processing certificate validation for " + request.RequestURI);
    log("CareMate Cert Validator: Environment: " + certConfig.environment + ", Enabled: " + currentConfig.enabled);
    
    // Skip validation if disabled
    if (!currentConfig.enabled) {
        log("CareMate Cert Validator: Certificate validation disabled for environment: " + certConfig.environment);
        return CareMateClientCertValidator.ReturnData(request, {});
    }
    
    // Extract certificate information
    var certInfo = extractCertInfo(request);
    
    // Check if client certificate is required
    if (currentConfig.requireClientCert && !certInfo) {
        log("CareMate Cert Validator: Client certificate required but not provided");
        request.ReturnOverrides.ResponseCode = 401;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Client certificate required",
            error: "CLIENT_CERT_REQUIRED",
            timestamp: new Date().toISOString()
        });
        return CareMateClientCertValidator.ReturnData(request, {});
    }
    
    // If certificate is present and verification is enabled, validate it
    if (certInfo && currentConfig.verifyClientCert) {
        log("CareMate Cert Validator: Validating client certificate - Subject: " + certInfo.subject);
        
        // Validate certificate expiry
        var expiryValidation = validateCertExpiry(certInfo);
        if (!expiryValidation.valid) {
            log("CareMate Cert Validator: Certificate expiry validation failed: " + expiryValidation.reason);
            request.ReturnOverrides.ResponseCode = 401;
            request.ReturnOverrides.ResponseError = JSON.stringify({
                status: false,
                message: "Certificate validation failed",
                error: "CERT_EXPIRED",
                reason: expiryValidation.reason,
                expiry: expiryValidation.expiry,
                timestamp: new Date().toISOString()
            });
            return CareMateClientCertValidator.ReturnData(request, {});
        }
        
        // Validate against allowed certificates
        var allowedValidation = validateAllowedCert(certInfo, currentConfig);
        if (!allowedValidation.valid) {
            log("CareMate Cert Validator: Certificate not in allowed list: " + allowedValidation.reason);
            request.ReturnOverrides.ResponseCode = 403;
            request.ReturnOverrides.ResponseError = JSON.stringify({
                status: false,
                message: "Certificate not authorized",
                error: "CERT_NOT_ALLOWED",
                reason: allowedValidation.reason,
                commonName: allowedValidation.commonName,
                timestamp: new Date().toISOString()
            });
            return CareMateClientCertValidator.ReturnData(request, {});
        }
        
        // Validate certificate pinning (production only)
        var pinningValidation = validateCertificatePinning(certInfo, currentConfig);
        if (!pinningValidation.valid) {
            log("CareMate Cert Validator: Certificate pinning validation failed: " + pinningValidation.reason);
            request.ReturnOverrides.ResponseCode = 403;
            request.ReturnOverrides.ResponseError = JSON.stringify({
                status: false,
                message: "Certificate pinning validation failed",
                error: "CERT_PINNING_FAILED",
                reason: pinningValidation.reason,
                fingerprint: pinningValidation.fingerprint,
                timestamp: new Date().toISOString()
            });
            return CareMateClientCertValidator.ReturnData(request, {});
        }
        
        // Add certificate information to request headers for downstream processing
        request.SetHeaders['X-Caremate-Client-Cert-Valid'] = 'true';
        request.SetHeaders['X-Caremate-Client-Cert-Subject'] = certInfo.subject || '';
        request.SetHeaders['X-Caremate-Client-Cert-Fingerprint'] = certInfo.fingerprint || '';
        request.SetHeaders['X-Caremate-Client-Cert-Serial'] = certInfo.serial || '';
        
        log("CareMate Cert Validator: Certificate validation successful for: " + certInfo.subject);
    } else if (certInfo) {
        // Certificate present but verification disabled
        request.SetHeaders['X-Caremate-Client-Cert-Present'] = 'true';
        request.SetHeaders['X-Caremate-Client-Cert-Subject'] = certInfo.subject || '';
        log("CareMate Cert Validator: Certificate present but verification disabled");
    }
    
    return CareMateClientCertValidator.ReturnData(request, {});
});

// Export the middleware
CareMateClientCertValidator;
