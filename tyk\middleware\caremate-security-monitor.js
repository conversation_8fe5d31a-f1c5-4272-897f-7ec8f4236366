// CareMate Security Monitoring and Alerting Middleware for Tyk Gateway
// Implements comprehensive security event logging, monitoring, and alerting

var CareMateSecurityMonitor = new TykJS.TykMiddleware.NewMiddleware({});

// Security monitoring configuration
var monitorConfig = {
    // Alert thresholds
    thresholds: {
        failedAuthAttempts: {
            count: 5,
            windowMinutes: 5,
            severity: "high"
        },
        suspiciousRequests: {
            count: 10,
            windowMinutes: 1,
            severity: "medium"
        },
        rateLimitExceeded: {
            count: 3,
            windowMinutes: 1,
            severity: "low"
        },
        invalidCertificates: {
            count: 3,
            windowMinutes: 5,
            severity: "high"
        },
        blockedIPs: {
            count: 1,
            windowMinutes: 1,
            severity: "medium"
        }
    },
    
    // Security event types
    eventTypes: {
        FAILED_AUTH: "failed_authentication",
        SUSPICIOUS_REQUEST: "suspicious_request",
        RATE_LIMIT_EXCEEDED: "rate_limit_exceeded",
        INVALID_CERT: "invalid_certificate",
        BLOCKED_IP: "blocked_ip",
        SQL_INJECTION_ATTEMPT: "sql_injection_attempt",
        XSS_ATTEMPT: "xss_attempt",
        PATH_TRAVERSAL_ATTEMPT: "path_traversal_attempt",
        MALICIOUS_USER_AGENT: "malicious_user_agent",
        LARGE_REQUEST: "large_request",
        INVALID_CONTENT_TYPE: "invalid_content_type"
    },
    
    // Suspicious patterns for detection
    suspiciousPatterns: [
        {
            name: "SQL Injection",
            pattern: /(?:union|select|insert|delete|drop|create|alter|exec|script)/i,
            severity: "high",
            eventType: "SQL_INJECTION_ATTEMPT"
        },
        {
            name: "XSS Attempt",
            pattern: /(?:<script|javascript:|vbscript:|onload=|onerror=)/i,
            severity: "high",
            eventType: "XSS_ATTEMPT"
        },
        {
            name: "Path Traversal",
            pattern: /(?:\.\.[\\/\\]|\.\.%2f|\.\.%5c)/i,
            severity: "medium",
            eventType: "PATH_TRAVERSAL_ATTEMPT"
        },
        {
            name: "Command Injection",
            pattern: /(?:cmd\.exe|powershell|bash|sh|\/bin\/)/i,
            severity: "high",
            eventType: "COMMAND_INJECTION_ATTEMPT"
        }
    ],
    
    // Malicious user agents
    maliciousUserAgents: [
        /sqlmap/i,
        /nikto/i,
        /nmap/i,
        /masscan/i,
        /nessus/i,
        /openvas/i,
        /burp/i,
        /w3af/i,
        /acunetix/i,
        /netsparker/i
    ]
};

// In-memory event store (in production, use Redis or database)
var securityEvents = {};
var alertCounts = {};

// Helper function to get current timestamp
function getCurrentTimestamp() {
    return Math.floor(Date.now() / 1000);
}

// Helper function to clean old events
function cleanOldEvents(windowMinutes) {
    var cutoffTime = getCurrentTimestamp() - (windowMinutes * 60);
    
    for (var key in securityEvents) {
        if (securityEvents[key]) {
            securityEvents[key] = securityEvents[key].filter(function(event) {
                return event.timestamp > cutoffTime;
            });
            
            if (securityEvents[key].length === 0) {
                delete securityEvents[key];
            }
        }
    }
}

// Helper function to record security event
function recordSecurityEvent(eventType, details) {
    var timestamp = getCurrentTimestamp();
    var eventKey = eventType + ":" + (details.clientIP || "unknown");
    
    if (!securityEvents[eventKey]) {
        securityEvents[eventKey] = [];
    }
    
    var event = {
        timestamp: timestamp,
        eventType: eventType,
        details: details,
        severity: details.severity || "medium"
    };
    
    securityEvents[eventKey].push(event);
    
    // Log the event
    log("CareMate Security Monitor: " + eventType + " - " + JSON.stringify(details));
    
    return event;
}

// Helper function to check alert thresholds
function checkAlertThresholds(eventType, clientIP) {
    var threshold = monitorConfig.thresholds[eventType];
    if (!threshold) return false;
    
    var eventKey = eventType + ":" + clientIP;
    var events = securityEvents[eventKey] || [];
    var windowStart = getCurrentTimestamp() - (threshold.windowMinutes * 60);
    
    var recentEvents = events.filter(function(event) {
        return event.timestamp > windowStart;
    });
    
    if (recentEvents.length >= threshold.count) {
        // Trigger alert
        triggerSecurityAlert(eventType, clientIP, recentEvents, threshold);
        return true;
    }
    
    return false;
}

// Helper function to trigger security alerts
function triggerSecurityAlert(eventType, clientIP, events, threshold) {
    var alertKey = "alert:" + eventType + ":" + clientIP;
    var now = getCurrentTimestamp();
    
    // Prevent alert spam - only alert once per 5 minutes for same event/IP
    if (alertCounts[alertKey] && (now - alertCounts[alertKey]) < 300) {
        return;
    }
    
    alertCounts[alertKey] = now;
    
    var alert = {
        timestamp: now,
        alertType: "SECURITY_THRESHOLD_EXCEEDED",
        eventType: eventType,
        clientIP: clientIP,
        severity: threshold.severity,
        eventCount: events.length,
        windowMinutes: threshold.windowMinutes,
        threshold: threshold.count,
        recentEvents: events.slice(-5) // Last 5 events
    };
    
    // Log critical alert
    log("CareMate Security Monitor: SECURITY ALERT - " + JSON.stringify(alert));
    
    // In production, send to alerting system (email, Slack, PagerDuty, etc.)
    sendAlert(alert);
}

// Helper function to send alerts (placeholder for production implementation)
function sendAlert(alert) {
    // Production implementation would send to:
    // - Email notifications
    // - Slack/Teams webhooks
    // - PagerDuty/OpsGenie
    // - SIEM systems
    // - Security dashboards
    
    log("CareMate Security Monitor: Alert would be sent to monitoring systems: " + alert.alertType);
}

// Helper function to detect suspicious patterns
function detectSuspiciousPatterns(request) {
    var suspiciousEvents = [];
    var url = decodeURIComponent(request.RequestURI);
    var userAgent = request.Headers["user-agent"] || request.Headers["User-Agent"] || "";
    
    // Check URL patterns
    for (var i = 0; i < monitorConfig.suspiciousPatterns.length; i++) {
        var pattern = monitorConfig.suspiciousPatterns[i];
        if (pattern.pattern.test(url)) {
            suspiciousEvents.push({
                type: pattern.eventType,
                name: pattern.name,
                severity: pattern.severity,
                location: "url",
                pattern: pattern.pattern.toString()
            });
        }
    }
    
    // Check user agent patterns
    for (var j = 0; j < monitorConfig.maliciousUserAgents.length; j++) {
        if (monitorConfig.maliciousUserAgents[j].test(userAgent)) {
            suspiciousEvents.push({
                type: "MALICIOUS_USER_AGENT",
                name: "Malicious User Agent",
                severity: "medium",
                location: "user_agent",
                userAgent: userAgent
            });
            break;
        }
    }
    
    return suspiciousEvents;
}

// Main security monitoring middleware
CareMateSecurityMonitor.NewProcessRequest(function(request, session, config) {
    // Clean old events periodically
    if (Math.random() < 0.01) { // 1% chance to clean on each request
        cleanOldEvents(60); // Clean events older than 1 hour
    }
    
    // Get client information
    var clientIP = request.RemoteAddr;
    if (request.Headers["x-forwarded-for"]) {
        clientIP = request.Headers["x-forwarded-for"].split(',')[0].trim();
    } else if (request.Headers["x-real-ip"]) {
        clientIP = request.Headers["x-real-ip"];
    }
    
    var userAgent = request.Headers["user-agent"] || request.Headers["User-Agent"] || "";
    var method = request.Method.toUpperCase();
    var path = request.RequestURI;
    
    // Check for security events from previous middleware
    var securityValidated = request.Headers["x-caremate-security-validated"];
    var authorized = request.Headers["x-caremate-authorized"];
    var rateLimitExceeded = request.Headers["x-ratelimit-exceeded"];
    var certValid = request.Headers["x-caremate-client-cert-valid"];
    
    // Record failed authentication attempts
    if (authorized !== "true" && path.indexOf("/auth/") !== -1) {
        recordSecurityEvent("FAILED_AUTH", {
            clientIP: clientIP,
            userAgent: userAgent,
            path: path,
            method: method,
            severity: "medium"
        });
        checkAlertThresholds("FAILED_AUTH", clientIP);
    }
    
    // Record rate limit exceeded events
    if (rateLimitExceeded === "true") {
        recordSecurityEvent("RATE_LIMIT_EXCEEDED", {
            clientIP: clientIP,
            userAgent: userAgent,
            path: path,
            method: method,
            severity: "low"
        });
        checkAlertThresholds("RATE_LIMIT_EXCEEDED", clientIP);
    }
    
    // Record invalid certificate events
    if (certValid === "false") {
        recordSecurityEvent("INVALID_CERT", {
            clientIP: clientIP,
            userAgent: userAgent,
            path: path,
            method: method,
            severity: "high"
        });
        checkAlertThresholds("INVALID_CERT", clientIP);
    }
    
    // Detect suspicious patterns
    var suspiciousEvents = detectSuspiciousPatterns(request);
    for (var i = 0; i < suspiciousEvents.length; i++) {
        var suspiciousEvent = suspiciousEvents[i];
        recordSecurityEvent(suspiciousEvent.type, {
            clientIP: clientIP,
            userAgent: userAgent,
            path: path,
            method: method,
            severity: suspiciousEvent.severity,
            patternName: suspiciousEvent.name,
            location: suspiciousEvent.location
        });
        checkAlertThresholds("SUSPICIOUS_REQUEST", clientIP);
    }
    
    // Add monitoring headers
    request.SetHeaders['X-Caremate-Security-Monitor'] = 'active';
    request.SetHeaders['X-Caremate-Monitor-Timestamp'] = new Date().toISOString();
    
    return CareMateSecurityMonitor.ReturnData(request, {});
});

// Export the middleware
CareMateSecurityMonitor;
