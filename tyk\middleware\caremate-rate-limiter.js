// CareMate Advanced Rate Limiting Middleware for Tyk Gateway
// Implements granular rate limiting based on endpoint, user role, and IP patterns

var CareMateRateLimiter = new TykJS.TykMiddleware.NewMiddleware({});

// Rate limiting configuration
var rateLimitConfig = {
    // Endpoint-specific limits (requests per minute)
    endpointLimits: {
        "POST:/auth/login": { rate: 10, window: 60, burst: 5 },
        "POST:/auth/register": { rate: 5, window: 60, burst: 2 },
        "POST:/auth/forgot-password": { rate: 3, window: 60, burst: 1 },
        "POST:/auth/reset-password": { rate: 3, window: 60, burst: 1 },
        "PUT:/user/password": { rate: 5, window: 60, burst: 2 },
        "DELETE:/user": { rate: 2, window: 60, burst: 1 },
        "POST:/user/role": { rate: 10, window: 60, burst: 3 },
        "PUT:/user/role": { rate: 10, window: 60, burst: 3 },
        "POST:/bulk": { rate: 5, window: 60, burst: 2 },
        "PUT:/bulk": { rate: 5, window: 60, burst: 2 },
        "DELETE:/bulk": { rate: 2, window: 60, burst: 1 }
    },
    
    // IP-based limits
    ipLimits: {
        default: { rate: 1000, window: 60, burst: 100 },
        suspicious: { rate: 10, window: 60, burst: 5 },
        trusted: { rate: 10000, window: 60, burst: 1000 }
    },
    
    // User role-based limits
    roleLimits: {
        admin: { rate: 2000, window: 60, burst: 200 },
        user: { rate: 1000, window: 60, burst: 100 },
        guest: { rate: 100, window: 60, burst: 20 },
        integration: { rate: 5000, window: 60, burst: 500 }
    },
    
    // Suspicious patterns that trigger stricter limits
    suspiciousPatterns: [
        /(?:union|select|insert|delete|drop|create|alter|exec|script)/i,
        /(?:<script|javascript:|vbscript:|onload=|onerror=)/i,
        /(?:\.\.[\\/\\]|\.\.%2f|\.\.%5c)/i,
        /(?:cmd\.exe|powershell|bash|sh|\/bin\/)/i
    ],
    
    // Trusted IP ranges (higher limits)
    trustedIPs: [
        "127.0.0.1",
        "::1",
        "***********/24"
    ]
};

// In-memory rate limiting store (in production, use Redis)
var rateLimitStore = {};

// Helper function to get current timestamp in seconds
function getCurrentTimestamp() {
    return Math.floor(Date.now() / 1000);
}

// Helper function to check if IP is in trusted range
function isTrustedIP(ip) {
    for (var i = 0; i < rateLimitConfig.trustedIPs.length; i++) {
        var trustedIP = rateLimitConfig.trustedIPs[i];
        if (trustedIP.indexOf('/') === -1) {
            if (ip === trustedIP) return true;
        } else {
            // Simple CIDR check for IPv4
            var parts = trustedIP.split('/');
            var rangeIP = parts[0];
            var mask = parseInt(parts[1]);
            
            if (ip.indexOf(':') === -1 && rangeIP.indexOf(':') === -1) {
                var ipParts = ip.split('.').map(function(part) { return parseInt(part); });
                var rangeParts = rangeIP.split('.').map(function(part) { return parseInt(part); });
                
                var ipNum = (ipParts[0] << 24) + (ipParts[1] << 16) + (ipParts[2] << 8) + ipParts[3];
                var rangeNum = (rangeParts[0] << 24) + (rangeParts[1] << 16) + (rangeParts[2] << 8) + rangeParts[3];
                var maskNum = (-1 << (32 - mask)) >>> 0;
                
                if ((ipNum & maskNum) === (rangeNum & maskNum)) return true;
            }
        }
    }
    return false;
}

// Helper function to check for suspicious patterns
function isSuspiciousRequest(request) {
    var url = decodeURIComponent(request.RequestURI);
    var userAgent = request.Headers["user-agent"] || request.Headers["User-Agent"] || "";
    
    for (var i = 0; i < rateLimitConfig.suspiciousPatterns.length; i++) {
        if (rateLimitConfig.suspiciousPatterns[i].test(url) || 
            rateLimitConfig.suspiciousPatterns[i].test(userAgent)) {
            return true;
        }
    }
    return false;
}

// Rate limiting function
function checkRateLimit(key, limit, window, burst) {
    var now = getCurrentTimestamp();
    var windowStart = now - window;
    
    if (!rateLimitStore[key]) {
        rateLimitStore[key] = {
            requests: [],
            burstCount: 0,
            lastReset: now
        };
    }
    
    var store = rateLimitStore[key];
    
    // Clean old requests outside the window
    store.requests = store.requests.filter(function(timestamp) {
        return timestamp > windowStart;
    });
    
    // Reset burst counter if window has passed
    if (now - store.lastReset > window) {
        store.burstCount = 0;
        store.lastReset = now;
    }
    
    // Check burst limit
    if (burst && store.burstCount >= burst) {
        return {
            allowed: false,
            reason: "burst_limit_exceeded",
            remaining: 0,
            resetTime: store.lastReset + window
        };
    }
    
    // Check rate limit
    if (store.requests.length >= limit) {
        return {
            allowed: false,
            reason: "rate_limit_exceeded",
            remaining: 0,
            resetTime: store.requests[0] + window
        };
    }
    
    // Allow request
    store.requests.push(now);
    store.burstCount++;
    
    return {
        allowed: true,
        remaining: limit - store.requests.length,
        resetTime: store.lastReset + window
    };
}

// Main rate limiting middleware
CareMateRateLimiter.NewProcessRequest(function(request, session, config) {
    log("CareMate Rate Limiter: Processing request for " + request.RequestURI);
    
    // Get client IP
    var clientIP = request.RemoteAddr;
    if (request.Headers["x-forwarded-for"]) {
        clientIP = request.Headers["x-forwarded-for"].split(',')[0].trim();
    } else if (request.Headers["x-real-ip"]) {
        clientIP = request.Headers["x-real-ip"];
    }
    
    // Get path and method
    var fullPath = request.RequestURI.split('?')[0];
    var method = request.Method.toUpperCase();
    var path = fullPath;
    
    // Strip listen path prefix
    if (fullPath.indexOf('/caremate/protected/') === 0) {
        path = fullPath.substring('/caremate/protected'.length);
    } else if (fullPath.indexOf('/caremate/api/') === 0) {
        path = fullPath.substring('/caremate/api'.length);
    }
    
    var endpointKey = method + ":" + path;
    
    // Determine IP category
    var ipCategory = "default";
    if (isTrustedIP(clientIP)) {
        ipCategory = "trusted";
    } else if (isSuspiciousRequest(request)) {
        ipCategory = "suspicious";
        log("CareMate Rate Limiter: Suspicious request detected from " + clientIP);
    }
    
    // Get applicable limits
    var endpointLimit = rateLimitConfig.endpointLimits[endpointKey];
    var ipLimit = rateLimitConfig.ipLimits[ipCategory];
    
    // Check endpoint-specific rate limit
    if (endpointLimit) {
        var endpointKey = "endpoint:" + endpointKey + ":" + clientIP;
        var endpointResult = checkRateLimit(
            endpointKey, 
            endpointLimit.rate, 
            endpointLimit.window, 
            endpointLimit.burst
        );
        
        if (!endpointResult.allowed) {
            log("CareMate Rate Limiter: Endpoint rate limit exceeded for " + endpointKey);
            request.ReturnOverrides.ResponseCode = 429;
            request.ReturnOverrides.ResponseError = JSON.stringify({
                status: false,
                message: "Rate limit exceeded for this endpoint",
                error: "ENDPOINT_RATE_LIMIT_EXCEEDED",
                reason: endpointResult.reason,
                resetTime: endpointResult.resetTime,
                endpoint: endpointKey
            });
            return CareMateRateLimiter.ReturnData(request, {});
        }
    }
    
    // Check IP-based rate limit
    var ipKey = "ip:" + clientIP;
    var ipResult = checkRateLimit(
        ipKey,
        ipLimit.rate,
        ipLimit.window,
        ipLimit.burst
    );
    
    if (!ipResult.allowed) {
        log("CareMate Rate Limiter: IP rate limit exceeded for " + clientIP);
        request.ReturnOverrides.ResponseCode = 429;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Rate limit exceeded for your IP address",
            error: "IP_RATE_LIMIT_EXCEEDED",
            reason: ipResult.reason,
            resetTime: ipResult.resetTime,
            ipCategory: ipCategory
        });
        return CareMateRateLimiter.ReturnData(request, {});
    }
    
    // Add rate limit headers
    request.SetHeaders['X-RateLimit-Limit'] = ipLimit.rate.toString();
    request.SetHeaders['X-RateLimit-Remaining'] = ipResult.remaining.toString();
    request.SetHeaders['X-RateLimit-Reset'] = ipResult.resetTime.toString();
    request.SetHeaders['X-RateLimit-Category'] = ipCategory;
    
    log("CareMate Rate Limiter: Request allowed for " + clientIP + " (" + ipCategory + ")");
    
    return CareMateRateLimiter.ReturnData(request, {});
});

// Export the middleware
CareMateRateLimiter;
