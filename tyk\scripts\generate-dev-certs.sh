#!/bin/bash

# CareMate Development Certificate Generation Script
# This script generates self-signed certificates for development and testing

set -e

# Configuration
CERT_DIR="./certs/development"
DAYS_VALID=365
KEY_SIZE=2048

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}CareMate Development Certificate Generator${NC}"
echo "=========================================="

# Create certificate directory
mkdir -p "$CERT_DIR"
cd "$CERT_DIR"

echo -e "${YELLOW}Creating Certificate Authority (CA)...${NC}"

# Generate CA private key
openssl genrsa -out ca.key $KEY_SIZE

# Generate CA certificate
openssl req -new -x509 -days $DAYS_VALID -key ca.key -out ca.crt -subj "/C=US/ST=Development/L=Local/O=CareMate/OU=Development/CN=CareMate Development CA"

echo -e "${GREEN}✓ CA certificate created${NC}"

echo -e "${YELLOW}Creating Server Certificate...${NC}"

# Generate server private key
openssl genrsa -out server.key $KEY_SIZE

# Generate server certificate signing request
openssl req -new -key server.key -out server.csr -subj "/C=US/ST=Development/L=Local/O=CareMate/OU=Gateway/CN=localhost"

# Create server certificate extensions
cat > server.ext << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = caremate.local
DNS.4 = *.caremate.local
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = ************
EOF

# Generate server certificate
openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt -days $DAYS_VALID -extensions v3_req -extfile server.ext

echo -e "${GREEN}✓ Server certificate created${NC}"

echo -e "${YELLOW}Creating Client Certificates...${NC}"

# Generate API client certificate
openssl genrsa -out api-client.key $KEY_SIZE
openssl req -new -key api-client.key -out api-client.csr -subj "/C=US/ST=Development/L=Local/O=CareMate/OU=API/CN=development-api-client"

# Create client certificate extensions
cat > client.ext << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth
EOF

openssl x509 -req -in api-client.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out api-client.crt -days $DAYS_VALID -extensions v3_req -extfile client.ext

echo -e "${GREEN}✓ API client certificate created${NC}"

# Generate admin client certificate
openssl genrsa -out admin-client.key $KEY_SIZE
openssl req -new -key admin-client.key -out admin-client.csr -subj "/C=US/ST=Development/L=Local/O=CareMate/OU=Admin/CN=development-admin-client"
openssl x509 -req -in admin-client.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out admin-client.crt -days $DAYS_VALID -extensions v3_req -extfile client.ext

echo -e "${GREEN}✓ Admin client certificate created${NC}"

# Generate integration client certificate
openssl genrsa -out integration-client.key $KEY_SIZE
openssl req -new -key integration-client.key -out integration-client.csr -subj "/C=US/ST=Development/L=Local/O=CareMate/OU=Integration/CN=development-integration-client"
openssl x509 -req -in integration-client.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out integration-client.crt -days $DAYS_VALID -extensions v3_req -extfile client.ext

echo -e "${GREEN}✓ Integration client certificate created${NC}"

# Create PEM bundles
cat server.crt ca.crt > server-bundle.pem
cat api-client.crt ca.crt > api-client-bundle.pem
cat admin-client.crt ca.crt > admin-client-bundle.pem
cat integration-client.crt ca.crt > integration-client-bundle.pem

echo -e "${GREEN}✓ Certificate bundles created${NC}"

# Generate certificate fingerprints for pinning
echo -e "${YELLOW}Generating certificate fingerprints...${NC}"
echo "CA Certificate Fingerprint:"
openssl x509 -noout -fingerprint -sha256 -inform pem -in ca.crt | cut -d= -f2

echo "Server Certificate Fingerprint:"
openssl x509 -noout -fingerprint -sha256 -inform pem -in server.crt | cut -d= -f2

echo "API Client Certificate Fingerprint:"
openssl x509 -noout -fingerprint -sha256 -inform pem -in api-client.crt | cut -d= -f2

# Set appropriate permissions
chmod 600 *.key
chmod 644 *.crt *.pem

# Clean up temporary files
rm -f *.csr *.ext *.srl

echo -e "${GREEN}✓ Certificate generation complete!${NC}"
echo ""
echo "Certificates created in: $(pwd)"
echo ""
echo "Files created:"
echo "  - ca.crt (Certificate Authority)"
echo "  - ca.key (CA Private Key)"
echo "  - server.crt (Server Certificate)"
echo "  - server.key (Server Private Key)"
echo "  - api-client.crt (API Client Certificate)"
echo "  - api-client.key (API Client Private Key)"
echo "  - admin-client.crt (Admin Client Certificate)"
echo "  - admin-client.key (Admin Client Private Key)"
echo "  - integration-client.crt (Integration Client Certificate)"
echo "  - integration-client.key (Integration Client Private Key)"
echo "  - *-bundle.pem (Certificate bundles with CA)"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Update Tyk configuration to use these certificates"
echo "2. Configure your API server to use the server certificate"
echo "3. Distribute client certificates to authorized clients"
echo "4. Update environment configuration to enable mTLS"
echo ""
echo -e "${RED}WARNING: These are development certificates only!${NC}"
echo -e "${RED}Do NOT use in production environments!${NC}"
