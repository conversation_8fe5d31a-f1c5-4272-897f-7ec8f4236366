{"id": "caremate-security-policy", "name": "CareMate Security Policy", "org_id": "default", "rate": 1000, "per": 60, "quota_max": 10000, "quota_renewal_rate": 3600, "access_rights": {"caremate-jwt": {"api_name": "CareMate JWT API", "api_id": "caremate-jwt", "versions": ["<PERSON><PERSON><PERSON>"], "allowed_urls": [], "restricted_types": [], "limit": {"rate": 1000, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 3, "max_query_depth": -1, "quota_max": 10000, "quota_renews": 1, "quota_remaining": 10000, "quota_renewal_rate": 3600, "set_by_policy": true}, "field_access_rights": [], "disable_introspection": false}, "caremate-public": {"api_name": "CareMate Public API", "api_id": "caremate-public", "versions": ["<PERSON><PERSON><PERSON>"], "allowed_urls": [], "restricted_types": [], "limit": {"rate": 100, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 3, "max_query_depth": -1, "quota_max": 1000, "quota_renews": 1, "quota_remaining": 1000, "quota_renewal_rate": 3600, "set_by_policy": true}, "field_access_rights": [], "disable_introspection": false}}, "hmac_enabled": false, "enable_http_signature_validation": false, "is_inactive": false, "tags": ["caremate", "security", "production"], "key_expires_in": 0, "partitions": {"quota": false, "rate_limit": false, "complexity": false, "acl": false}, "last_updated": "1640995200", "state": "active"}