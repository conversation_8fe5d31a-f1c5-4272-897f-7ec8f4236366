{"name": "CareMate JWT API", "api_id": "caremate-jwt", "org_id": "default", "definition": {"location": "header", "key": "version"}, "auth": {"auth_header_name": "Authorization"}, "enable_jwt": true, "use_keyless": false, "jwt_signing_method": "hmac", "jwt_source": "header:Authorization", "jwt_identity_base_field": "sub", "jwt_client_base_field": "", "jwt_policy_field_name": "", "jwt_default_policies": [], "jwt_issued_at_validation_skew": 0, "jwt_expires_at_validation_skew": 0, "jwt_not_before_validation_skew": 0, "jwt_skip_kid": false, "jwt_scope_to_policy_mapping": {}, "jwt_scope_claim_name": "", "jwt_secret": "$JWT_SECRET", "custom_middleware": {"driver": "otto", "pre": [{"name": "CareMateSecurityMiddleware", "path": "./middleware/caremate-security.js", "require_session": false, "raw_body_only": false}, {"name": "CareMateRateLimiter", "path": "./middleware/caremate-rate-limiter.js", "require_session": false, "raw_body_only": false}, {"name": "CareMateTransform", "path": "./middleware/caremate-transform.js", "require_session": false, "raw_body_only": false}], "post": [{"name": "CareMateAuth", "path": "./middleware/caremate-auth.js", "require_session": false, "raw_body_only": false}], "response": [{"name": "CareMateTransform", "path": "./middleware/caremate-transform.js", "require_session": false, "raw_body_only": false}]}, "version_data": {"not_versioned": true, "versions": {"Default": {"name": "<PERSON><PERSON><PERSON>"}}}, "allowed_ips": [], "blacklisted_ips": [], "enable_ip_whitelisting": false, "enable_ip_blacklisting": false, "global_rate_limit": {"rate": 1000, "per": 60}, "global_rate_limit_disabled": false, "strip_auth_data": false, "enable_batch_request_support": false, "enable_detailed_recording": true, "tags": ["caremate", "protected", "jwt"], "CORS": {"enable": true, "allowed_origins": ["http://localhost:3000", "http://localhost:3001"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "allowed_headers": ["Origin", "Accept", "Content-Type", "X-Requested-With", "Authorization", "X-Caremate-*"], "exposed_headers": ["X-Caremate-*"], "allow_credentials": true, "max_age": 24, "options_passthrough": false, "debug": false}, "response_processors": [{"name": "header_injector", "options": {"add_headers": {"X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block", "Strict-Transport-Security": "max-age=31536000; includeSubDomains", "Referrer-Policy": "strict-origin-when-cross-origin"}, "remove_headers": ["Server", "X-Powered-By"]}}], "request_size_limit": 1048576, "proxy": {"listen_path": "/caremate/protected/", "target_url": "http://************:3001/api/", "strip_listen_path": true, "preserve_host_header": false, "disable_strip_slash": false, "check_host_against_uptime_tests": false}}