#!/usr/bin/env node

/**
 * Comprehensive Security Testing Script for CareMate Tyk Gateway
 * Tests all security layers including bypass attempts and penetration testing
 */

const axios = require('axios');
const crypto = require('crypto');

// Configuration
const CONFIG = {
  // API endpoints
  apiUrl: 'http://localhost:3001/api',
  tykUrl: 'http://localhost:8181',
  protectedUrl: 'http://localhost:8181/caremate/protected',
  publicUrl: 'http://localhost:8181/caremate/api',
  
  // Test credentials
  testUser: {
    email: '<EMAIL>',
    password: 'Pa$w0rd!'
  },
  
  // JWT secret for testing
  jwtSecret: 'THIS_IS_MY_NEW_KEY_FOR_FUTURE'
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

/**
 * Utility function to log test results
 */
function logTest(name, passed, message, severity = 'info') {
  const result = {
    name,
    passed,
    message,
    severity,
    timestamp: new Date().toISOString()
  };
  
  testResults.tests.push(result);
  
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${name}: ${message}`);
  } else {
    if (severity === 'warning') {
      testResults.warnings++;
      console.log(`⚠️  ${name}: ${message}`);
    } else {
      testResults.failed++;
      console.log(`❌ ${name}: ${message}`);
    }
  }
}

/**
 * Test 1: Basic Gateway Connectivity
 */
async function testGatewayConnectivity() {
  console.log('\n🔍 Testing Gateway Connectivity...');
  
  try {
    const response = await axios.get(`${CONFIG.tykUrl}/hello`, { timeout: 5000 });
    logTest('Gateway Connectivity', true, 'Tyk Gateway is accessible');
  } catch (error) {
    logTest('Gateway Connectivity', false, `Gateway not accessible: ${error.message}`, 'error');
  }
  
  try {
    const response = await axios.get(`${CONFIG.apiUrl}/health`, { timeout: 5000 });
    logTest('API Connectivity', true, 'API server is accessible');
  } catch (error) {
    logTest('API Connectivity', false, `API not accessible: ${error.message}`, 'error');
  }
}

/**
 * Test 2: Authentication Flow
 */
async function testAuthenticationFlow() {
  console.log('\n🔐 Testing Authentication Flow...');
  
  try {
    // Test login through gateway
    const loginResponse = await axios.post(`${CONFIG.publicUrl}/auth/login`, {
      email: CONFIG.testUser.email,
      password: CONFIG.testUser.password
    });
    
    if (loginResponse.data.tokens && loginResponse.data.tokens.access) {
      logTest('Gateway Authentication', true, 'Login successful through gateway');
      return loginResponse.data.tokens.access.token;
    } else {
      logTest('Gateway Authentication', false, 'Login failed - no token received');
      return null;
    }
  } catch (error) {
    logTest('Gateway Authentication', false, `Login failed: ${error.response?.data?.message || error.message}`);
    return null;
  }
}

/**
 * Test 3: JWT Validation at Gateway Level
 */
async function testJWTValidation(token) {
  console.log('\n🎫 Testing JWT Validation...');
  
  if (!token) {
    logTest('JWT Validation', false, 'No token available for testing');
    return;
  }
  
  try {
    // Test with valid token
    const response = await axios.get(`${CONFIG.protectedUrl}/user`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logTest('Valid JWT', true, 'Valid JWT accepted by gateway');
  } catch (error) {
    logTest('Valid JWT', false, `Valid JWT rejected: ${error.response?.status} ${error.response?.statusText}`);
  }
  
  try {
    // Test with invalid token
    const response = await axios.get(`${CONFIG.protectedUrl}/user`, {
      headers: { Authorization: 'Bearer invalid.token.here' }
    });
    logTest('Invalid JWT', false, 'Invalid JWT was accepted (SECURITY ISSUE!)');
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      logTest('Invalid JWT', true, 'Invalid JWT properly rejected');
    } else {
      logTest('Invalid JWT', false, `Unexpected error: ${error.response?.status}`);
    }
  }
}

/**
 * Test 4: Direct API Access (Bypass Attempts)
 */
async function testDirectAPIAccess(token) {
  console.log('\n🚫 Testing Direct API Access (Bypass Attempts)...');
  
  try {
    // Attempt to access API directly without Tyk headers
    const response = await axios.get(`${CONFIG.apiUrl}/user`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logTest('Direct API Access', false, 'Direct API access allowed (SECURITY ISSUE!)');
  } catch (error) {
    if (error.response?.status === 403) {
      logTest('Direct API Access', true, 'Direct API access properly blocked');
    } else if (error.response?.status === 401) {
      logTest('Direct API Access', true, 'Direct API access blocked by authentication');
    } else {
      logTest('Direct API Access', false, `Unexpected response: ${error.response?.status}`);
    }
  }
  
  try {
    // Attempt with spoofed Tyk headers
    const response = await axios.get(`${CONFIG.apiUrl}/user`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'x-caremate-identity-id': 'spoofed-user',
        'x-caremate-permissions': '["admin"]',
        'x-caremate-authorized': 'true',
        'x-caremate-security-validated': 'true',
        'x-caremate-timestamp': new Date().toISOString()
      }
    });
    logTest('Header Spoofing', false, 'Spoofed headers accepted (SECURITY ISSUE!)');
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      logTest('Header Spoofing', true, 'Spoofed headers properly rejected');
    } else {
      logTest('Header Spoofing', false, `Unexpected response: ${error.response?.status}`);
    }
  }
}

/**
 * Test 5: Rate Limiting
 */
async function testRateLimiting() {
  console.log('\n⏱️  Testing Rate Limiting...');
  
  const requests = [];
  const maxRequests = 15; // Should exceed rate limits
  
  for (let i = 0; i < maxRequests; i++) {
    requests.push(
      axios.get(`${CONFIG.publicUrl}/health`, { timeout: 2000 })
        .catch(error => ({ error: true, status: error.response?.status }))
    );
  }
  
  try {
    const responses = await Promise.all(requests);
    const rateLimited = responses.filter(r => r.error && r.status === 429);
    
    if (rateLimited.length > 0) {
      logTest('Rate Limiting', true, `Rate limiting active - ${rateLimited.length} requests blocked`);
    } else {
      logTest('Rate Limiting', false, 'Rate limiting not working - all requests allowed');
    }
  } catch (error) {
    logTest('Rate Limiting', false, `Rate limiting test failed: ${error.message}`);
  }
}

/**
 * Test 6: Malicious Pattern Detection
 */
async function testMaliciousPatterns() {
  console.log('\n🛡️  Testing Malicious Pattern Detection...');
  
  const maliciousPatterns = [
    { name: 'SQL Injection', path: '/health?id=1\' OR 1=1--' },
    { name: 'XSS Attempt', path: '/health?search=<script>alert(1)</script>' },
    { name: 'Path Traversal', path: '/health/../../../etc/passwd' },
    { name: 'Command Injection', path: '/health?cmd=ls;cat /etc/passwd' }
  ];
  
  for (const pattern of maliciousPatterns) {
    try {
      const response = await axios.get(`${CONFIG.publicUrl}${pattern.path}`, { timeout: 2000 });
      logTest(`${pattern.name} Detection`, false, `Malicious pattern not blocked: ${pattern.name}`);
    } catch (error) {
      if (error.response?.status === 400 || error.response?.status === 403) {
        logTest(`${pattern.name} Detection`, true, `Malicious pattern blocked: ${pattern.name}`);
      } else {
        logTest(`${pattern.name} Detection`, false, `Unexpected response: ${error.response?.status}`);
      }
    }
  }
}

/**
 * Test 7: Security Headers
 */
async function testSecurityHeaders() {
  console.log('\n🔒 Testing Security Headers...');
  
  try {
    const response = await axios.get(`${CONFIG.publicUrl}/health`);
    const headers = response.headers;
    
    const expectedHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'strict-transport-security',
      'referrer-policy'
    ];
    
    let headerCount = 0;
    for (const header of expectedHeaders) {
      if (headers[header]) {
        headerCount++;
      }
    }
    
    if (headerCount >= expectedHeaders.length * 0.8) { // At least 80% of headers
      logTest('Security Headers', true, `${headerCount}/${expectedHeaders.length} security headers present`);
    } else {
      logTest('Security Headers', false, `Only ${headerCount}/${expectedHeaders.length} security headers present`);
    }
  } catch (error) {
    logTest('Security Headers', false, `Failed to test security headers: ${error.message}`);
  }
}

/**
 * Test 8: CORS Configuration
 */
async function testCORSConfiguration() {
  console.log('\n🌐 Testing CORS Configuration...');
  
  try {
    const response = await axios.options(`${CONFIG.publicUrl}/health`, {
      headers: {
        'Origin': 'https://malicious-site.com',
        'Access-Control-Request-Method': 'GET'
      }
    });
    
    const corsHeader = response.headers['access-control-allow-origin'];
    if (corsHeader === '*') {
      logTest('CORS Configuration', false, 'CORS allows all origins (potential security issue)');
    } else if (corsHeader) {
      logTest('CORS Configuration', true, `CORS properly configured: ${corsHeader}`);
    } else {
      logTest('CORS Configuration', true, 'CORS properly restrictive');
    }
  } catch (error) {
    logTest('CORS Configuration', true, 'CORS properly blocks unauthorized origins');
  }
}

/**
 * Test 9: Request Size Limits
 */
async function testRequestSizeLimits() {
  console.log('\n📏 Testing Request Size Limits...');
  
  try {
    // Create a large payload (2MB)
    const largePayload = 'x'.repeat(2 * 1024 * 1024);
    
    const response = await axios.post(`${CONFIG.publicUrl}/auth/login`, {
      email: CONFIG.testUser.email,
      password: CONFIG.testUser.password,
      largeData: largePayload
    }, { timeout: 5000 });
    
    logTest('Request Size Limits', false, 'Large request accepted (potential DoS vulnerability)');
  } catch (error) {
    if (error.response?.status === 413 || error.code === 'ECONNRESET') {
      logTest('Request Size Limits', true, 'Large requests properly rejected');
    } else {
      logTest('Request Size Limits', false, `Unexpected error: ${error.message}`);
    }
  }
}

/**
 * Test 10: Emergency Access
 */
async function testEmergencyAccess() {
  console.log('\n🚨 Testing Emergency Access...');
  
  try {
    // Test health endpoint direct access (should be allowed from localhost)
    const response = await axios.get(`${CONFIG.apiUrl}/health`, { timeout: 2000 });
    
    if (response.headers['x-caremate-warning']) {
      logTest('Emergency Access', true, 'Emergency access working with warnings');
    } else {
      logTest('Emergency Access', false, 'Emergency access not properly flagged');
    }
  } catch (error) {
    logTest('Emergency Access', false, `Emergency access failed: ${error.message}`);
  }
}

/**
 * Main test runner
 */
async function runSecurityTests() {
  console.log('🔐 CareMate Security Testing Suite');
  console.log('=====================================\n');
  
  // Run all tests
  await testGatewayConnectivity();
  
  const token = await testAuthenticationFlow();
  
  await testJWTValidation(token);
  await testDirectAPIAccess(token);
  await testRateLimiting();
  await testMaliciousPatterns();
  await testSecurityHeaders();
  await testCORSConfiguration();
  await testRequestSizeLimits();
  await testEmergencyAccess();
  
  // Print summary
  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⚠️  Warnings: ${testResults.warnings}`);
  console.log(`📝 Total Tests: ${testResults.tests.length}`);
  
  const successRate = (testResults.passed / testResults.tests.length) * 100;
  console.log(`📈 Success Rate: ${successRate.toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.passed && t.severity !== 'warning')
      .forEach(t => console.log(`   - ${t.name}: ${t.message}`));
  }
  
  if (testResults.warnings > 0) {
    console.log('\n⚠️  Warnings:');
    testResults.tests
      .filter(t => !t.passed && t.severity === 'warning')
      .forEach(t => console.log(`   - ${t.name}: ${t.message}`));
  }
  
  // Security score
  const securityScore = Math.max(0, 100 - (testResults.failed * 10) - (testResults.warnings * 5));
  console.log(`\n🛡️  Security Score: ${securityScore}/100`);
  
  if (securityScore >= 90) {
    console.log('🎉 Excellent security posture!');
  } else if (securityScore >= 70) {
    console.log('👍 Good security, but room for improvement');
  } else {
    console.log('⚠️  Security needs attention!');
  }
  
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Run tests if called directly
if (require.main === module) {
  runSecurityTests().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = { runSecurityTests, testResults };
