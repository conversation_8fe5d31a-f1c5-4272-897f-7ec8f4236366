{"policies": {"caremate-admin-policy": {"id": "caremate-admin-policy", "name": "CareMate Admin Policy", "org_id": "default", "rate": 2000, "per": 60, "quota_max": 50000, "quota_renewal_rate": 3600, "access_rights": {"caremate-jwt": {"api_name": "CareMate JWT API", "api_id": "caremate-jwt", "versions": ["<PERSON><PERSON><PERSON>"], "limit": {"rate": 2000, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 5, "quota_max": 50000, "quota_renewal_rate": 3600}}, "caremate-public": {"api_name": "CareMate Public API", "api_id": "caremate-public", "versions": ["<PERSON><PERSON><PERSON>"], "limit": {"rate": 500, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 3, "quota_max": 10000, "quota_renewal_rate": 3600}}}, "tags": ["admin", "high-privilege"], "is_inactive": false, "state": "active"}, "caremate-user-policy": {"id": "caremate-user-policy", "name": "CareMate User Policy", "org_id": "default", "rate": 1000, "per": 60, "quota_max": 20000, "quota_renewal_rate": 3600, "access_rights": {"caremate-jwt": {"api_name": "CareMate JWT API", "api_id": "caremate-jwt", "versions": ["<PERSON><PERSON><PERSON>"], "limit": {"rate": 1000, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 3, "quota_max": 20000, "quota_renewal_rate": 3600}}, "caremate-public": {"api_name": "CareMate Public API", "api_id": "caremate-public", "versions": ["<PERSON><PERSON><PERSON>"], "limit": {"rate": 200, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 3, "quota_max": 5000, "quota_renewal_rate": 3600}}}, "tags": ["user", "standard"], "is_inactive": false, "state": "active"}, "caremate-guest-policy": {"id": "caremate-guest-policy", "name": "CareMate Guest Policy", "org_id": "default", "rate": 100, "per": 60, "quota_max": 1000, "quota_renewal_rate": 3600, "access_rights": {"caremate-public": {"api_name": "CareMate Public API", "api_id": "caremate-public", "versions": ["<PERSON><PERSON><PERSON>"], "limit": {"rate": 100, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 2, "quota_max": 1000, "quota_renewal_rate": 3600}}}, "tags": ["guest", "limited"], "is_inactive": false, "state": "active"}, "caremate-api-integration-policy": {"id": "caremate-api-integration-policy", "name": "CareMate API Integration Policy", "org_id": "default", "rate": 5000, "per": 60, "quota_max": 100000, "quota_renewal_rate": 3600, "access_rights": {"caremate-jwt": {"api_name": "CareMate JWT API", "api_id": "caremate-jwt", "versions": ["<PERSON><PERSON><PERSON>"], "limit": {"rate": 5000, "per": 60, "throttle_interval": 1, "throttle_retry_limit": 10, "quota_max": 100000, "quota_renewal_rate": 3600}}}, "tags": ["integration", "high-volume"], "is_inactive": false, "state": "active"}}, "endpoint_specific_limits": {"auth_endpoints": {"rate": 10, "per": 60, "burst": 5, "endpoints": ["POST:/auth/login", "POST:/auth/register", "POST:/auth/forgot-password", "POST:/auth/reset-password"]}, "sensitive_endpoints": {"rate": 50, "per": 60, "burst": 10, "endpoints": ["PUT:/user/password", "DELETE:/user", "POST:/user/role", "PUT:/user/role"]}, "bulk_endpoints": {"rate": 5, "per": 60, "burst": 2, "endpoints": ["POST:/bulk/*", "PUT:/bulk/*", "DELETE:/bulk/*"]}}, "ip_based_limits": {"default": {"rate": 1000, "per": 60, "burst": 100}, "suspicious": {"rate": 10, "per": 60, "burst": 5}, "trusted": {"rate": 10000, "per": 60, "burst": 1000}}}