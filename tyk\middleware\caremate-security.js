// CareMate Security Middleware for Tyk Gateway
// Implements multi-layered security including IP filtering, request validation, and threat detection
// This middleware runs BEFORE authentication to provide defense in depth

var CareMateSecurityMiddleware = new TykJS.TykMiddleware.NewMiddleware({});

// Security configuration - in production, load from environment or config file
var securityConfig = {
    // IP Access Control
    enableIPWhitelist: false, // Set to true in production
    enableIPBlacklist: true,
    allowedIPs: [
        "127.0.0.1",
        "::1",
        "***********/24",
        "10.0.0.0/8",
        "**********/12"
    ],
    blockedIPs: [
        // Add known malicious IPs here
    ],
    
    // Request validation
    maxRequestSize: 1048576, // 1MB
    maxHeaderSize: 8192,     // 8KB
    maxUrlLength: 2048,      // 2KB
    
    // Rate limiting per IP
    rateLimitPerIP: {
        requests: 100,
        windowSeconds: 60
    },
    
    // Suspicious patterns (basic WAF functionality)
    suspiciousPatterns: [
        /(?:union|select|insert|delete|drop|create|alter|exec|script)/i,
        /(?:<script|javascript:|vbscript:|onload=|onerror=)/i,
        /(?:\.\.[\\/\\]|\.\.%2f|\.\.%5c)/i,
        /(?:cmd\.exe|powershell|bash|sh|\/bin\/)/i,
        /(?:\||;|&|`|\$\(|\${)/,
        /(?:eval\(|setTimeout\(|setInterval\()/i
    ],
    
    // Blocked user agents
    blockedUserAgents: [
        /sqlmap/i,
        /nikto/i,
        /nmap/i,
        /masscan/i,
        /nessus/i,
        /openvas/i,
        /burp/i,
        /w3af/i,
        /acunetix/i,
        /netsparker/i
    ],
    
    // Required headers (relaxed for testing)
    requiredHeaders: []
};

// IP address validation functions
function isIPInRange(ip, range) {
    if (range.indexOf('/') === -1) {
        return ip === range;
    }
    
    // Simple CIDR check (basic implementation)
    var parts = range.split('/');
    var rangeIP = parts[0];
    var mask = parseInt(parts[1]);
    
    // For IPv4 only (basic implementation)
    if (ip.indexOf(':') === -1 && rangeIP.indexOf(':') === -1) {
        var ipParts = ip.split('.').map(function(part) { return parseInt(part); });
        var rangeParts = rangeIP.split('.').map(function(part) { return parseInt(part); });
        
        var ipNum = (ipParts[0] << 24) + (ipParts[1] << 16) + (ipParts[2] << 8) + ipParts[3];
        var rangeNum = (rangeParts[0] << 24) + (rangeParts[1] << 16) + (rangeParts[2] << 8) + rangeParts[3];
        var maskNum = (-1 << (32 - mask)) >>> 0;
        
        return (ipNum & maskNum) === (rangeNum & maskNum);
    }
    
    return false;
}

function isIPAllowed(ip) {
    if (!securityConfig.enableIPWhitelist) {
        return true;
    }
    
    for (var i = 0; i < securityConfig.allowedIPs.length; i++) {
        if (isIPInRange(ip, securityConfig.allowedIPs[i])) {
            return true;
        }
    }
    return false;
}

function isIPBlocked(ip) {
    if (!securityConfig.enableIPBlacklist) {
        return false;
    }
    
    for (var i = 0; i < securityConfig.blockedIPs.length; i++) {
        if (isIPInRange(ip, securityConfig.blockedIPs[i])) {
            return true;
        }
    }
    return false;
}

// Request validation functions
function validateRequest(request) {
    var errors = [];
    
    // Check request size
    if (request.ContentLength > securityConfig.maxRequestSize) {
        errors.push("Request size exceeds maximum allowed (" + securityConfig.maxRequestSize + " bytes)");
    }
    
    // Check URL length
    if (request.RequestURI.length > securityConfig.maxUrlLength) {
        errors.push("URL length exceeds maximum allowed (" + securityConfig.maxUrlLength + " characters)");
    }
    
    // Check for required headers
    for (var i = 0; i < securityConfig.requiredHeaders.length; i++) {
        var header = securityConfig.requiredHeaders[i].toLowerCase();
        if (!request.Headers[header] && !request.Headers[header.charAt(0).toUpperCase() + header.slice(1)]) {
            errors.push("Missing required header: " + header);
        }
    }
    
    // Check user agent against blocked list
    var userAgent = request.Headers["user-agent"] || request.Headers["User-Agent"] || "";
    for (var j = 0; j < securityConfig.blockedUserAgents.length; j++) {
        if (securityConfig.blockedUserAgents[j].test(userAgent)) {
            errors.push("Blocked user agent detected");
            break;
        }
    }
    
    // Check for suspicious patterns in URL and headers
    var urlToCheck = decodeURIComponent(request.RequestURI);
    for (var k = 0; k < securityConfig.suspiciousPatterns.length; k++) {
        if (securityConfig.suspiciousPatterns[k].test(urlToCheck)) {
            errors.push("Suspicious pattern detected in URL");
            break;
        }
    }
    
    return errors;
}

// Main security middleware function
CareMateSecurityMiddleware.NewProcessRequest(function(request, session, config) {
    log("CareMate Security: Processing security checks for " + request.RequestURI);
    
    // Get client IP (handle proxy headers)
    var clientIP = request.RemoteAddr || "unknown";
    if (request.Headers["x-forwarded-for"]) {
        clientIP = request.Headers["x-forwarded-for"].split(',')[0].trim();
    } else if (request.Headers["x-real-ip"]) {
        clientIP = request.Headers["x-real-ip"];
    }

    // Ensure clientIP is not undefined
    if (!clientIP) {
        clientIP = "unknown";
    }
    
    log("CareMate Security: Client IP: " + clientIP);
    
    // IP whitelist check
    if (!isIPAllowed(clientIP)) {
        log("CareMate Security: IP not in whitelist: " + clientIP);
        request.ReturnOverrides.ResponseCode = 403;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Access denied: IP address not authorized",
            error: "IP_NOT_WHITELISTED",
            timestamp: new Date().toISOString()
        });
        return CareMateSecurityMiddleware.ReturnData(request, {});
    }
    
    // IP blacklist check
    if (isIPBlocked(clientIP)) {
        log("CareMate Security: Blocked IP detected: " + clientIP);
        request.ReturnOverrides.ResponseCode = 403;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Access denied: IP address blocked",
            error: "IP_BLACKLISTED",
            timestamp: new Date().toISOString()
        });
        return CareMateSecurityMiddleware.ReturnData(request, {});
    }
    
    // Request validation
    var validationErrors = validateRequest(request);
    if (validationErrors.length > 0) {
        log("CareMate Security: Request validation failed: " + validationErrors.join(", "));
        request.ReturnOverrides.ResponseCode = 400;
        request.ReturnOverrides.ResponseError = JSON.stringify({
            status: false,
            message: "Request validation failed",
            error: "INVALID_REQUEST",
            details: validationErrors,
            timestamp: new Date().toISOString()
        });
        return CareMateSecurityMiddleware.ReturnData(request, {});
    }
    
    // Add security headers to request for downstream processing
    request.SetHeaders['x-caremate-client-ip'] = clientIP;
    request.SetHeaders['x-caremate-security-validated'] = 'true';
    request.SetHeaders['x-caremate-security-timestamp'] = new Date().toISOString();
    
    log("CareMate Security: All security checks passed for " + clientIP);
    
    // Continue to next middleware
    return CareMateSecurityMiddleware.ReturnData(request, {});
});

// Export the middleware
CareMateSecurityMiddleware;
