#!/usr/bin/env node

/**
 * Simple Anti-Spoofing Test for CareMate API
 * Tests that the API rejects spoofed Tyk headers
 */

const axios = require('axios');

// Configuration
const CONFIG = {
  apiUrl: 'http://localhost:3001/api',
  tykUrl: 'http://localhost:8181',
  protectedUrl: 'http://localhost:8181/caremate/protected',
  publicUrl: 'http://localhost:8181/caremate/api',
  
  testUser: {
    email: '<EMAIL>',
    password: 'Pa$w0rd!'
  }
};

/**
 * Test 1: Normal authentication through Tyk (should work)
 */
async function testNormalAuth() {
  console.log('🔐 Testing normal authentication through Tyk...');
  
  try {
    // Login through gateway
    const loginResponse = await axios.post(`${CONFIG.publicUrl}/auth/login`, {
      email: CONFIG.testUser.email,
      password: CONFIG.testUser.password
    });
    
    if (loginResponse.data.tokens && loginResponse.data.tokens.access) {
      console.log('✅ Normal authentication works');
      return loginResponse.data.tokens.access.token;
    } else {
      console.log('❌ Normal authentication failed');
      return null;
    }
  } catch (error) {
    console.log('❌ Normal authentication failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * Test 2: Access protected endpoint through Tyk (should work)
 */
async function testProtectedAccess(token) {
  console.log('🛡️  Testing protected endpoint access through Tyk...');
  
  if (!token) {
    console.log('❌ No token available for testing');
    return;
  }
  
  try {
    const response = await axios.get(`${CONFIG.protectedUrl}/user`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Protected endpoint access through Tyk works');
  } catch (error) {
    console.log('❌ Protected endpoint access failed:', error.response?.status, error.response?.statusText);
  }
}

/**
 * Test 3: Direct API access with spoofed headers (should fail)
 */
async function testSpoofedHeaders(token) {
  console.log('🚫 Testing direct API access with spoofed headers...');
  
  if (!token) {
    console.log('❌ No token available for testing');
    return;
  }
  
  try {
    // Attempt direct API access with fake Tyk headers
    const response = await axios.get(`${CONFIG.apiUrl}/user`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'x-caremate-identity-id': 'fake-user-id',
        'x-caremate-permissions': '["admin", "super_admin"]',
        'x-caremate-authorized': 'true',
        'x-caremate-timestamp': new Date().toISOString(),
        'x-caremate-signature': 'fake-signature-12345'
      }
    });
    
    console.log('❌ SECURITY ISSUE: Spoofed headers were accepted!');
    console.log('Response:', response.data);
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Spoofed headers properly rejected (401 Unauthorized)');
    } else if (error.response?.status === 403) {
      console.log('✅ Spoofed headers properly rejected (403 Forbidden)');
    } else {
      console.log('⚠️  Unexpected response:', error.response?.status, error.response?.statusText);
    }
  }
}

/**
 * Test 4: Direct API access without headers (should fail)
 */
async function testDirectAccess(token) {
  console.log('🚪 Testing direct API access without Tyk headers...');
  
  if (!token) {
    console.log('❌ No token available for testing');
    return;
  }
  
  try {
    // Attempt direct API access without any Tyk headers
    const response = await axios.get(`${CONFIG.apiUrl}/user`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('❌ SECURITY ISSUE: Direct API access was allowed!');
    console.log('Response:', response.data);
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Direct API access properly rejected (401 Unauthorized)');
    } else if (error.response?.status === 403) {
      console.log('✅ Direct API access properly rejected (403 Forbidden)');
    } else {
      console.log('⚠️  Unexpected response:', error.response?.status, error.response?.statusText);
    }
  }
}

/**
 * Test 5: Health endpoint access (should work for both)
 */
async function testHealthEndpoints() {
  console.log('❤️  Testing health endpoints...');
  
  try {
    // Test direct API health
    const directHealth = await axios.get(`${CONFIG.apiUrl}/health`);
    console.log('✅ Direct API health endpoint works');
  } catch (error) {
    console.log('⚠️  Direct API health endpoint failed:', error.message);
  }
  
  try {
    // Test Tyk gateway health
    const tykHealth = await axios.get(`${CONFIG.tykUrl}/hello`);
    console.log('✅ Tyk gateway health endpoint works');
  } catch (error) {
    console.log('⚠️  Tyk gateway health endpoint failed:', error.message);
  }
}

/**
 * Main test runner
 */
async function runAntiSpoofingTests() {
  console.log('🔒 CareMate Anti-Spoofing Security Test');
  console.log('=====================================\n');
  
  // Test health endpoints first
  await testHealthEndpoints();
  console.log('');
  
  // Test normal authentication
  const token = await testNormalAuth();
  console.log('');
  
  // Test protected access through Tyk
  await testProtectedAccess(token);
  console.log('');
  
  // Test spoofed headers (should fail)
  await testSpoofedHeaders(token);
  console.log('');
  
  // Test direct access (should fail)
  await testDirectAccess(token);
  console.log('');
  
  console.log('📊 Test Summary');
  console.log('===============');
  console.log('If you see "✅ Spoofed headers properly rejected" and');
  console.log('"✅ Direct API access properly rejected", then the');
  console.log('anti-spoofing protection is working correctly!');
  console.log('');
  console.log('If you see any "❌ SECURITY ISSUE" messages,');
  console.log('then there are vulnerabilities that need fixing.');
}

// Run tests if called directly
if (require.main === module) {
  runAntiSpoofingTests().catch(error => {
    console.error('Test failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runAntiSpoofingTests };
