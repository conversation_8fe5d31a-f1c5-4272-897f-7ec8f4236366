# CareMate Tyk Gateway

## About
This repository contains the Tyk Gateway configuration for the CareMate API project. The Tyk Gateway acts as an API gateway providing authentication, authorization, analytics, and rate-limiting capabilities for the CareMate API.

## Architecture Overview
The CareMate project consists of two main components:
- **API Project** (`../api/`) - The main API server that handles business logic
- **Tyk Gateway** (`./`) - API gateway that provides authentication, authorization, and routing

### Authentication Modes
The API project can be configured to work in two authentication modes via the `AUTH_MODE` environment variable:

1. **Custom Mode** (`AUTH_MODE=custom`) - API handles authentication directly using JWT tokens
2. **Tyk Mode** (`AUTH_MODE=tyk`) - Tyk Gateway handles authentication and passes validated user information to the API

When `AUTH_MODE=tyk`, the API project uses different server URLs:
- `PUBLIC_SERVER_URL` - For public endpoints routed through Tyk (e.g., `http://localhost:8181/caremate/api`)
- `PROTECTED_SERVER_URL` - For protected endpoints routed through Tyk (e.g., `http://localhost:8181/caremate/protected`)

## Project Structure

### API Definitions (`./apps/`)
- `caremate-public-api.json` - Public endpoints (authentication, health checks, master data)
- `caremate-jwt-api.json` - JWT-protected endpoints with permission-based authorization

### Security Middleware (`./middleware/`)
- `caremate-security.js` - Multi-layered security validation (IP filtering, request validation, threat detection)
- `caremate-rate-limiter.js` - Advanced rate limiting with endpoint-specific and IP-based controls
- `caremate-transform.js` - Request/response transformation and data sanitization
- `caremate-cert-validator.js` - Client certificate validation and mutual TLS enforcement
- `caremate-auth.js` - Permission-based authorization middleware for protected endpoints
- `caremate-public.js` - Middleware for public endpoints that sets dummy headers for API compatibility
- `caremate-security-monitor.js` - Security event monitoring, alerting, and audit logging

### Security Configuration (`./security/`)
- `ip-whitelist.json` - IP access control configuration
- `mtls-config.json` - Mutual TLS and certificate validation settings
- `environment-config.js` - Environment-specific security configurations

### Policies (`./policies/`)
- `caremate-security-policy.json` - Comprehensive security policies
- `caremate-rate-limiting.json` - Granular rate limiting policies

### Scripts (`./scripts/`)
- `generate-dev-certs.sh` - Development certificate generation script

### Configuration
- `tyk.standalone.conf` - Tyk Gateway configuration with enhanced security settings
- `docker-compose.yml` - Docker setup for Tyk Gateway and Redis
- `test-caremate-auth.js` - Comprehensive test script for authentication and authorization
- `SECURITY.md` - Comprehensive security documentation

## Requirements
1. **Redis** - Tyk gateway requires a running Redis instance for session storage and analytics
2. **Docker & Docker Compose** - For running the gateway and Redis containers
3. **CareMate API** - The main API server running on port 3001

## Gateway Configuration

### Dual API Setup
The CareMate Tyk Gateway is configured with two separate API definitions:

1. **Public API** (`/caremate/api/`) - Keyless endpoints for:
   - Authentication (`/auth/login`, `/auth/register`)
   - Health checks (`/health`)
   - Master data (countries, states, timezones)
   - Site settings

2. **Protected API** (`/caremate/protected/`) - JWT-authenticated endpoints for:
   - All business logic endpoints requiring authentication
   - Permission-based authorization via middleware
   - User management, facilities, appointments, etc.

### Security-Enhanced Authentication Flow
1. **Request Security Validation**: Multi-layered security checks (IP filtering, rate limiting, request validation)
2. **Client Certificate Validation**: Mutual TLS verification (production environments)
3. **Client Authentication**: Client authenticates via `/caremate/api/auth/login` (public endpoint)
4. **JWT Token Issuance**: API returns JWT token with embedded user permissions
5. **Protected Request Processing**:
   - Client includes JWT token in Authorization header for protected endpoints
   - Security middleware validates request (IP, rate limits, patterns, certificates)
   - Tyk Gateway validates JWT signature at gateway level
   - Authorization middleware validates permissions and sets headers for API
   - Request transformation sanitizes and validates data
   - Security monitoring logs events and checks alert thresholds
6. **API Processing**: API receives validated user information via headers
7. **Response Security**: Response transformation adds security headers and sanitizes data

### Environment Configuration
The API project's authentication mode is controlled by the `AUTH_MODE` environment variable:

```bash
# In .env.local or .env.dev
AUTH_MODE=tyk  # Enable Tyk Gateway mode
# or
AUTH_MODE=custom  # Direct API authentication (bypass gateway)
```

## Security Features

### Multi-Layered Security Architecture
The CareMate Tyk Gateway implements a comprehensive security strategy to prevent unauthorized access and protect against various attack vectors:

#### 1. Network Security
- **IP Whitelisting/Blacklisting**: Configurable access control lists
- **Rate Limiting**: Per-IP and per-endpoint rate limiting with burst protection
- **Geographic Restrictions**: Block requests from specific regions
- **DDoS Protection**: Built-in protection against distributed attacks

#### 2. Authentication & Authorization
- **JWT Validation**: Proper JWT signature verification at gateway level (fixed keyless vulnerability)
- **Mutual TLS (mTLS)**: Client certificate authentication for production environments
- **Certificate Pinning**: Pin specific certificates for enhanced security
- **Permission-based Authorization**: Granular permission checking with role-based access

#### 3. Request/Response Security
- **Input Sanitization**: Remove malicious patterns and scripts
- **Content-Type Validation**: Ensure only allowed content types
- **Request Size Limits**: Prevent oversized requests
- **Security Headers**: Comprehensive security headers (HSTS, CSP, XSS protection, etc.)
- **Data Sanitization**: Remove sensitive information from logs and responses

#### 4. Threat Detection & Monitoring
- **Real-time Pattern Detection**: Monitor for SQL injection, XSS, path traversal attempts
- **Malicious User Agent Detection**: Block known attack tools
- **Security Event Logging**: Comprehensive audit trail
- **Automated Alerting**: Immediate notification of security events
- **Anomaly Detection**: Identify unusual behavior patterns

### Security Middleware Stack
1. **CareMateSecurityMiddleware** - IP filtering and basic validation
2. **CareMateRateLimiter** - Advanced rate limiting
3. **CareMateTransform** - Request sanitization and validation
4. **CareMateClientCertValidator** - Certificate validation (production)
5. **CareMateAuth** - JWT validation and authorization
6. **CareMateSecurityMonitor** - Event monitoring and alerting

### Environment-Specific Security
- **Development**: Relaxed security for testing
- **Staging**: Moderate security with IP restrictions
- **Production**: Maximum security with mTLS, certificate pinning, and strict validation

For detailed security documentation, see [SECURITY.md](./SECURITY.md).

When `AUTH_MODE=tyk`, the API uses gateway URLs:
- `PUBLIC_SERVER_URL=http://localhost:8181/caremate/api`
- `PROTECTED_SERVER_URL=http://localhost:8181/caremate/protected`

### Testing Setup
```bash
# Run the CareMate authentication test suite
node test-caremate-auth.js

# Test credentials available:
# Admin: <EMAIL> / Pa$w0rd!
# Kiosk: <EMAIL> / Pa$w0rd!
```

### Gateway Endpoints
- **Public API**: `http://localhost:8181/caremate/api/` (no authentication required)
- **Protected API**: `http://localhost:8181/caremate/protected/` (JWT token required)
- **Gateway Admin**: `http://localhost:8181/tyk/apis` (requires X-Tyk-Authorization header)

## Getting Started

### Prerequisites
Before starting, ensure you have the following installed:
- **Docker & Docker Compose** - For running Tyk Gateway and Redis
- **HTTP Client** - For testing API endpoints:
  - Command line: [curl](https://everything.curl.dev/get)
  - GUI: [Postman](https://www.postman.com/downloads/)
  - VS Code: [REST Client extension](https://marketplace.visualstudio.com/items?itemName=humao.rest-client)
- **jq** (optional) - For JSON formatting with curl commands

### Starting the Gateway

1. **Start Tyk Gateway and Redis**:
   ```bash
   docker-compose up -d
   ```

2. **Verify the deployment**:
   ```bash
   curl http://localhost:8181/hello -i
   ```

   Expected response:
   ```json
   {
     "status": "pass",
     "version": "v5.5.0",
     "description": "Tyk GW",
     "details": {
       "redis": {
         "status": "pass",
         "componentType": "datastore",
         "time": "2024-01-01T12:00:00Z"
       }
     }
   }
   ```

3. **Check loaded API definitions**:
   ```bash
   curl http://localhost:8181/tyk/apis -H "X-Tyk-Authorization: foo" | jq .
   ```

   This should return the CareMate API definitions (public and protected APIs).

### Starting the CareMate API

1. **Navigate to the API directory**:
   ```bash
   cd ../api
   ```

2. **Configure environment**:
   ```bash
   # Copy and configure environment file
   cp .env.local.example .env.local

   # Set AUTH_MODE to enable Tyk Gateway
   AUTH_MODE=tyk
   ```

3. **Start the API server**:
   ```bash
   npm install
   npm start
   ```

The API will be available at `http://localhost:3001` but should be accessed through the gateway URLs when `AUTH_MODE=tyk`.

## Testing the Setup

### Basic Gateway Tests

1. **Test public endpoints** (no authentication required):
   ```bash
   # Health check
   curl http://localhost:8181/caremate/api/health

   # Get countries (master data)
   curl http://localhost:8181/caremate/api/countries
   ```

2. **Test authentication**:
   ```bash
   # Login to get JWT token
   curl -X POST http://localhost:8181/caremate/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "Pa$w0rd!"
     }'
   ```

3. **Test protected endpoints** (requires JWT token):
   ```bash
   # Replace YOUR_JWT_TOKEN with the token from login response
   curl http://localhost:8181/caremate/protected/facilities \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

### Automated Testing

Run the comprehensive test suite:
```bash
node test-caremate-auth.js
```

This script tests:
- Public endpoint accessibility
- Authentication flow
- JWT token validation
- Permission-based authorization
- Header injection by middleware

## Configuration Details

### Environment Variables
The gateway uses environment variables from `docker-compose.yml`:
- `JWT_SECRET` - Shared secret for JWT validation (must match API project)

### API Definitions
- **Public API** (`caremate-public-api.json`):
  - Listen path: `/caremate/api/`
  - Target: `http://************:3001/api/`
  - Keyless (no authentication)
  - Uses `caremate-public.js` middleware

- **Protected API** (`caremate-jwt-api.json`):
  - Listen path: `/caremate/protected/`
  - Target: `http://************:3001/api/`
  - JWT authentication enabled
  - Uses `caremate-auth.js` middleware for permissions

### Middleware Functions
- **caremate-public.js**: Sets dummy headers for API compatibility in Tyk mode
- **caremate-auth.js**: Validates JWT tokens and checks user permissions

## Troubleshooting

### Common Issues

1. **Gateway not starting**:
   - Check if Redis is running: `docker-compose logs redis`
   - Verify port 8181 is not in use: `netstat -an | grep 8181`

2. **API definitions not loading**:
   - Check gateway logs: `docker-compose logs gateway`
   - Verify JSON syntax in `./apps/*.json` files

3. **JWT validation failing**:
   - Ensure `JWT_SECRET` matches between gateway and API project
   - Check token format: should be `Bearer <token>`

4. **Permission errors**:
   - Verify user permissions in JWT token payload
   - Check middleware logs for permission validation

5. **Target URL connection issues**:
   - Update target URLs in API definitions to match your API server
   - Ensure API server is accessible from Docker container

### Logs and Debugging

View gateway logs:
```bash
docker-compose logs -f gateway
```

View Redis logs:
```bash
docker-compose logs -f redis
```

Enable debug logging by modifying `tyk.standalone.conf`:
```json
{
  "log_level": "debug"
}
```